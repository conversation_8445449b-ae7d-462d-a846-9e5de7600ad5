const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type Media {
        id: ID!
        url: String!
        title: String!
        description: String
        party: Party!
        uploadedAt: DateTime!
        owner: User!
        tags: [Tag!]
    }

    input MediaFilterInput {
        title: String
        partyId: String
        party: PartyFilterInput
        uploadedAt: DateTimeRangeInput
        owner: UserFilterInput
        ownerId: String
        tags: [String!]
    }

    type MediaWrapper {
        media: Media!
    }

    type MediasWrapper {
        medias: [Media]!
    }

    type MediaResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MediaWrapper!
    }

    type MediasResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MediasWrapper!
        pagination: PaginationInfo!
    }

    type MediaErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MediaResult = MediaResponse | MediaErrorResponse
    union MediasResult = MediasResponse | MediaErrorResponse

    type Query {
        getMediaById(id: ID!): MediaResult!
        getMedias(filter: MediaFilterInput, pagination: PaginationInput): MediasResult!
    }

    input MediaInput {
        url: String!
        title: String!
        description: String
        tags: [ID!]
        party: ID
    }

    input MediaUpdateInput {
        url: String
        title: String
        description: String
        tags: [ID!]
        party: ID
    }

    type Mutation {
        createMedia(input: MediaInput!): MediaResult!
        updateMedia(id: ID!, input: MediaUpdateInput!): MediaResult!
        deleteMedia(id: ID!): MediaResult!
        favoriteMedia(id: ID!): MediaResult!
        unfavoriteMedia(id: ID!): MediaResult!
    }
`;