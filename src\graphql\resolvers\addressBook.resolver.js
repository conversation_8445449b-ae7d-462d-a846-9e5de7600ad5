const AddressBook = require('../../models/AddressBook');
const { User } = require('../../models/User');
const VenueAddress = require('../../models/VenueAddress');
const Party = require('../../models/Party');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildAddressBookQuery = require('../filters/addressBook.filter');
const { validateReferences } = require('../../utils/validation.util');
const { findReferences } = require('../../utils/referenceCheck.util');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');

const addressBookIdSchema = {
    venueAddress: { type: 'single', required: true, model: VenueAddress }
};

const addressBookIdUpdateSchema = {
    venueAddress: { type: 'single', required: false, model: VenueAddress }
};

const addressBookResolvers = {
    Query: {
        getAddressBookById: async (_, { id }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const addressBook = await AddressBook.findById(id)
                    .populate('user')
                    .populate('venueAddress');

                if (!addressBook) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Address book not found', {
                        errors: [{ field: 'id', message: 'Address book not found' }]
                    });
                }

                if (addressBook.user.toString() !== context.user._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this address book' }]
                    });
                }

                let cachedAddressBook = await getByIdCache(id);
                if (!cachedAddressBook) {
                    await setCache(id, addressBook);
                    cachedAddressBook = addressBook;
                }

                return createResponse('AddressBookResponse', 'SUCCESS', 'Address book retrieved successfully', {
                    result: { addressBook: cachedAddressBook }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error retrieving address book', {
                    errors: [{ field: 'getAddressBookById', message: error.message }]
                });
            }
        },

        getAddressBooks: async (_, { filter, pagination }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const query = await buildAddressBookQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const accessQuery = {
                    ...query,
                    user: context.user._id
                };

                const paginationInfo = await getPaginationInfo(AddressBook, accessQuery, limit, skip);

                const addressBooks = await AddressBook.find(accessQuery)
                    .populate('user')
                    .populate('venueAddress')
                    .skip(skip)
                    .limit(limit)
                    .sort({ createdAt: -1 });

                const updatedPaginationInfo = {
                    ...paginationInfo,
                    totalItems: paginationInfo.totalItems || 0,
                    totalPages: Math.ceil((paginationInfo.totalItems || 0) / limit)
                };

                if (addressBooks.length === 0) {
                    return createResponse('AddressBooksResponse', 'FAILURE', 'No address books found', {
                        result: { addressBooks },
                        pagination: updatedPaginationInfo
                    });
                }

                return createResponse('AddressBooksResponse', 'SUCCESS', 'Address books retrieved successfully', {
                    result: { addressBooks },
                    pagination: updatedPaginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error retrieving address books', {
                    errors: [{ field: 'getAddressBooks', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createAddressBook: async (_, { input }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const referenceValidationErrors = await validateReferences(input, addressBookIdSchema, 'AddressBook');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createAddressBook',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Invalid address book information', {
                        errors
                    });
                }

                const addressBook = new AddressBook({
                    ...input,
                    user: context.user._id
                });

                await addressBook.save();

                const populatedAddressBook = await AddressBook.findById(addressBook._id)
                    .populate('user')
                    .populate('venueAddress');

                return createResponse('AddressBookResponse', 'SUCCESS', 'Address book created successfully', {
                    result: { addressBook: populatedAddressBook }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error creating address book', {
                    errors: [{
                        field: 'addressBook',
                        message: error.message || 'An unexpected error occurred'
                    }]
                });
            }
        },

        updateAddressBook: async (_, { id, input }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const existingAddressBook = await AddressBook.findById(id);
                if (!existingAddressBook) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Address book not found', {
                        errors: [{ field: 'id', message: 'Address book not found' }]
                    });
                }

                console.log(existingAddressBook.user, context.user._id);

                if (existingAddressBook.user.toString() !== context.user._id.toString()) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this address book' }]
                    });
                }

                const validationError = await validateReferences(input, addressBookIdUpdateSchema, 'AddressBook');
                if (validationError) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const updatedAddressBook = await AddressBook.findByIdAndUpdate(
                    id,
                    { ...input, user: context.user._id },
                    { new: true }
                ).populate('user').populate('venueAddress');

                await clearCacheById(id);

                return createResponse('AddressBookResponse', 'SUCCESS', 'Address book updated successfully', {
                    result: { addressBook: updatedAddressBook }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error updating address book', {
                    errors: [{ field: 'updateAddressBook', message: error.message }]
                });
            }
        },

        deleteAddressBook: async (_, { id }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const addressBook = await AddressBook.findById(id)
                    .populate('user')
                    .populate('venueAddress');

                if (!addressBook) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Address book not found', {
                        errors: [{ field: 'id', message: 'Address book not found' }]
                    });
                }

                if (addressBook.user._id.toString() !== context.user._id.toString()) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this address book' }]
                    });
                }

                const references = await findReferences(id, 'AddressBook');
                if (references.length > 0) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Address book cannot be deleted', {
                        errors: [{ field: 'id', message: `Address book cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const result = await cascadeDelete('AddressBook', id);
                if (!result.success) {
                    return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error deleting address book', {
                        errors: [{ field: 'deleteAddressBook', message: result.error.message }]
                    });
                }

                await clearCacheById(id);
                return createResponse('AddressBookResponse', 'SUCCESS', 'Address book deleted successfully', {
                    result: { addressBook }
                });
            } catch (error) {
                console.error(error);
                return createResponse('AddressBookErrorResponse', 'FAILURE', 'Error deleting address book', {
                    errors: [{ field: 'deleteAddressBook', message: error.message }]
                });
            }
        }
    }
};

module.exports = addressBookResolvers; 