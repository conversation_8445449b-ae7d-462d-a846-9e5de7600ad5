const { createResponse } = require("../../utils/response.util");
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { validateReferences } = require('../../utils/validation.util');
const buildEventGroupInvitationQuery = require('./filters/eventGroupInvitation.filter');
const EventGroupInvitation = require("../../models/EventGroupInvitation");
const EventGroup = require("../../models/EventGroup");
const { User } = require("../../models/User");
const { hasEventGroupMemberLevelAccess, hasEventGroupOrganizerLevelAccess, hasEventGroupCoOrganizerLevelAccess } = require('../../utils/auth/accessLevels.util');

const eventGroupInvitationSchema = {
    eventGroup: { type: 'single', model: EventGroup }
}

const eventGroupInvitationResolvers = {
    EventGroupInvitation: {
        user: async (parent) => {
            try {
                return await User.findOne({ _id: parent.user });
            } catch (error) {
                console.error('Error in user resolver:', error);
                return null;
            }
        },
        eventGroup: async (parent) => {
            try {
                return await EventGroup.findOne({ _id: parent.eventGroup });
            } catch (error) {
                console.error('Error in eventGroup resolver:', error);
                return null;
            }
        }
    },

    Query: {
        getEventGroupInvitationById: async (_, { id }, context) => {
            try {
                const invitation = await EventGroupInvitation.findById(id);
                if (!invitation) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ message: 'Invitation not found' }]
                    });
                }

                const isRecipient = context.user._id.toString() === invitation.user.toString();
                const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, invitation.eventGroup);

                if (!isRecipient && !hasAccess) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Unauthorized access to invitation', {
                        errors: [{ message: 'Unauthorized access to invitation' }]
                    });
                }

                return createResponse('EventGroupInvitationResponse', 'SUCCESS', 'Invitation retrieved successfully', {
                    result: { eventGroupInvitation: invitation }
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        },

        getEventGroupInvitations: async (_, { filter = {}, pagination = {} }, context) => {
            try {
                const baseQuery = await buildEventGroupInvitationQuery(filter);

                const userGroups = await EventGroup.find({
                    $or: [
                        { members: context.user._id },
                        { organizers: context.user._id }
                    ]
                }).select('_id');

                const groupIds = userGroups.map(group => group._id);
                baseQuery.$or = [
                    { user: context.user._id },
                    { eventGroup: { $in: groupIds } }
                ];

                const { limit = 10, skip = 0 } = pagination;
                const paginationInfo = await getPaginationInfo(EventGroupInvitation, baseQuery, limit, skip);
                
                const invitations = await EventGroupInvitation.find(baseQuery)
                    .skip(skip)
                    .limit(limit)
                    .sort({ createdAt: -1 });

                return createResponse('EventGroupInvitationsResponse', 'SUCCESS', 'Invitations retrieved successfully', {
                    result: { eventGroupInvitations: invitations },
                    pagination: paginationInfo
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createEventGroupInvitation: async (_, { input }, context) => {
            try {
                const referenceValidationErrors = await validateReferences(input, eventGroupInvitationSchema, 'EventGroupInvitation');
                
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createEventGroupInvitation',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Invalid event group invitation information', {
                        errors
                    });
                }

                const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, input.eventGroupId);
                if (!hasAccess) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Unauthorized: Only group organizers can send invitations', {
                        errors: [{ message: 'Unauthorized: Only group organizers can send invitations' }]
                    });
                }

                const existingInvitation = await EventGroupInvitation.findOne({
                    user: input.userId,
                    eventGroup: input.eventGroupId
                });

                if (existingInvitation) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Invitation already exists', {
                        errors: [{ message: 'Invitation already exists' }]
                    });
                }

                const invitation = new EventGroupInvitation({
                    user: input.userId,
                    eventGroup: input.eventGroupId,
                    status: input.status || 'PENDING'
                });

                await invitation.save();

                await EventGroup.findByIdAndUpdate(
                    input.eventGroupId,
                    { $addToSet: { invitedMembers: input.userId } },
                    { new: true }
                );

                return createResponse('EventGroupInvitationResponse', 'SUCCESS', 'Invitation created successfully', {
                    result: { eventGroupInvitation: invitation }
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        },

        sendEventGroupInvitation: async (_, { input }, context) => {
            try {
                const hasAccess = await hasEventGroupCoOrganizerLevelAccess(context.user._id, input.eventGroupId);
                if (!hasAccess) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Unauthorized: Only group co-organizers can send invitations', {
                        errors: [{ message: 'Unauthorized: Only group co-organizers can send invitations' }]
                    });
                }

                const invitations = [];
                const userIds = [];

                const eventGroup = await EventGroup.findById(input.eventGroupId);
                if (!eventGroup) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Event group not found', {
                        errors: [{ message: 'Event group not found' }]
                    });
                }

                for (const member of input.members) {
                    let user = await User.findOne({ phone: member.phone });
                    
                    if (!user) {
                        user = new User({
                            firstName: member.firstName,
                            lastName: member.lastName || '',
                            phone: member.phone,
                            email: member.email || '',
                            isRegistered: false,
                            role: ['user']
                        });
                        await user.save();
                    }

                    const isOrganizer = eventGroup.organizers.includes(user._id);
                    const isMember = eventGroup.members.includes(user._id);
                    if (isOrganizer || isMember) {
                        continue;
                    }

                    const existingInvitation = await EventGroupInvitation.findOne({
                        user: user._id,
                        eventGroup: input.eventGroupId
                    });

                    if (existingInvitation) {
                        if (existingInvitation.status === 'REJECTED' || existingInvitation.status === 'ACCEPTED') {
                            existingInvitation.status = 'PENDING';
                            await existingInvitation.save();
                            invitations.push(existingInvitation);
                            userIds.push(user._id);
                        }
                        continue;
                    }

                    const invitation = new EventGroupInvitation({
                        user: user._id,
                        eventGroup: input.eventGroupId,
                        status: 'PENDING'
                    });
                    await invitation.save();
                    invitations.push(invitation);
                    userIds.push(user._id);
                }

                if (userIds.length > 0) {
                    await EventGroup.findByIdAndUpdate(
                        input.eventGroupId,
                        { $addToSet: { invitedMembers: { $each: userIds } } },
                        { new: true }
                    );
                }

                return createResponse('EventGroupInvitationResponse', 'SUCCESS', 'Invitations sent successfully', {
                    result: { eventGroupInvitation: invitations[0] }
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        },

        updateEventGroupInvitation: async (_, { id, input }, context) => {
            try {
                const invitation = await EventGroupInvitation.findById(id);
                if (!invitation) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ message: 'Invitation not found' }]
                    });
                }

                const isRecipient = context.user._id.toString() === invitation.user.toString();
                const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, invitation.eventGroup);

                if (!isRecipient && !hasAccess) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Unauthorized: Only the invited user or group organizers can update this invitation', {
                        errors: [{ message: 'Unauthorized: Only the invited user or group organizers can update this invitation' }]
                    });
                }

                const previousStatus = invitation.status;
                invitation.status = input.status;
                await invitation.save();

                if (input.status === 'ACCEPTED' && previousStatus !== 'ACCEPTED') {
                    await EventGroup.findByIdAndUpdate(
                        invitation.eventGroup,
                        { 
                            $addToSet: { members: invitation.user },
                            $pull: { invitedMembers: invitation.user }
                        },
                        { new: true }
                    );
                }

                return createResponse('EventGroupInvitationResponse', 'SUCCESS', 'Invitation updated successfully', {
                    result: { eventGroupInvitation: invitation }
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        },

        deleteEventGroupInvitation: async (_, { id }, context) => {
            try {
                const invitation = await EventGroupInvitation.findById(id);
                if (!invitation) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Invitation not found', {
                        errors: [{ message: 'Invitation not found' }]
                    });
                }

                const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, invitation.eventGroup);
                if (!hasAccess) {
                    return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', 'Unauthorized: Only group organizers can delete invitations', {
                        errors: [{ message: 'Unauthorized: Only group organizers can delete invitations' }]
                    });
                }

                await EventGroupInvitation.findByIdAndDelete(id);

                if (invitation.status === 'ACCEPTED') {
                    await EventGroup.findByIdAndUpdate(
                        invitation.eventGroup,
                        { 
                            $pull: { 
                                invitedMembers: invitation.user,
                                members: invitation.user,
                                organizers: invitation.user
                            } 
                        },
                        { new: true }
                    );
                } else {
                    await EventGroup.findByIdAndUpdate(
                        invitation.eventGroup,
                        { $pull: { invitedMembers: invitation.user } },
                        { new: true }
                    );
                }

                return createResponse('EventGroupInvitationResponse', 'SUCCESS', 'Invitation deleted successfully', {
                    result: { eventGroupInvitation: invitation }
                });
            } catch (error) {
                return createResponse('EventGroupInvitationErrorResponse', 'FAILURE', error.message, {
                    errors: [{ message: error.message }]
                });
            }
        }
    }
};

module.exports = eventGroupInvitationResolvers; 