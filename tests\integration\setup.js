const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');
const { createServer } = require('../../src/utils/serverSetup.util');
const express = require('express');
const typeDefs = require('../../src/graphql/typeDefs');
const resolvers = require('../../src/graphql/resolvers');

let mongoServer;
let testServer;
let app;

// Mock external services
jest.mock('../../src/utils/clerk.util', () => ({
  isValidJWT: jest.fn(() => true),
  verifyTokenLocally: jest.fn(() => Promise.resolve({
    sub: 'test-user-id',
    metadata: { role: ['user'] }
  })),
  verifyClerkUser: jest.fn(() => Promise.resolve(true))
}));

jest.mock('../../src/infrastructure/redisClient', () => {
  const mockRedisClient = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn()
  };
  return jest.fn(() => mockRedisClient);
});

// Mock AWS services
jest.mock('aws-sdk', () => ({
  S3: jest.fn(() => ({
    upload: jest.fn(() => ({
      promise: jest.fn(() => Promise.resolve({ Location: 'https://test-bucket.s3.amazonaws.com/test-file.jpg' }))
    })),
    deleteObject: jest.fn(() => ({
      promise: jest.fn(() => Promise.resolve())
    }))
  })),
  CloudWatch: jest.fn(() => ({
    putLogEvents: jest.fn(() => ({
      promise: jest.fn(() => Promise.resolve())
    }))
  }))
}));

// Mock notification services
jest.mock('../../src/utils/notificationProducer', () => ({
  sendNotification: jest.fn(() => Promise.resolve()),
  sendPushNotification: jest.fn(() => Promise.resolve())
}));

beforeAll(async () => {
  // Start in-memory MongoDB
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Override MongoDB connection string
  process.env.MONGODB = mongoUri;
  
  // Connect to the in-memory database
  await mongoose.connect(mongoUri);
  
  // Create test Express app and server
  app = express();
  testServer = await createServer(app, typeDefs, resolvers);
  
  // Make server available globally for tests
  global.testApp = app;
  global.testServer = testServer;
});

afterAll(async () => {
  // Clean up
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
  
  if (mongoServer) {
    await mongoServer.stop();
  }
  
  if (testServer && testServer.httpServer) {
    testServer.httpServer.close();
  }
});

beforeEach(async () => {
  // Clear all collections before each test
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    await collections[key].deleteMany({});
  }
});
