const { Graph<PERSON>TestHelper, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Authentication & Authorization Integration Tests', () => {
  let graphqlHelper;
  let testUser;
  let adminUser;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser({
      role: ['user'],
      externalId: 'test-user-id'
    });
    
    adminUser = await TestDataFactory.createUser({
      role: ['admin'],
      externalId: 'admin-user-id',
      email: '<EMAIL>',
      phone: '+1987654321'
    });
  });

  describe('Authentication Requirements', () => {
    it('should allow access to public queries without authentication', async () => {
      // Remove auth token
      graphqlHelper.setAuthToken('');

      const publicQuery = `
        query {
          hello
        }
      `;

      const response = await graphqlHelper.query(publicQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.hello).toBe('Hello, World!');
    });

    it('should allow user registration without authentication', async () => {
      graphqlHelper.setAuthToken('');

      const registerMutation = `
        mutation Register($input: RegisterInput!) {
          register(input: $input) {
            ... on UserResponse {
              status
              message
              result {
                user {
                  firstName
                  email
                }
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          role: ['user'],
          firstName: 'New',
          lastName: 'User',
          email: '<EMAIL>',
          phone: '+1111111111',
          isRegistered: true,
          externalId: 'new-user-external-id'
        }
      };

      const response = await graphqlHelper.mutation(registerMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.register.status).toBe('SUCCESS');
    });

    it('should require authentication for protected queries', async () => {
      graphqlHelper.setAuthToken('');

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });

    it('should require authentication for protected mutations', async () => {
      graphqlHelper.setAuthToken('');

      const protectedMutation = `
        mutation {
          updateUser(input: { firstName: "Updated" }) {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.mutation(protectedMutation);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });
  });

  describe('Token Validation', () => {
    it('should accept valid JWT token', async () => {
      AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
      graphqlHelper.setAuthToken('Bearer valid-token');

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
              result {
                user {
                  firstName
                }
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUser.status).toBe('SUCCESS');
    });

    it('should reject invalid JWT token format', async () => {
      graphqlHelper.setAuthToken('Bearer invalid.token.format');

      // Mock invalid token verification
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.isValidJWT.mockReturnValue(false);

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });

    it('should reject expired JWT token', async () => {
      graphqlHelper.setAuthToken('Bearer expired-token');

      // Mock expired token verification
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockRejectedValue(new Error('Token expired'));

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });

    it('should reject token with invalid signature', async () => {
      graphqlHelper.setAuthToken('Bearer tampered-token');

      // Mock signature verification failure
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockRejectedValue(new Error('Invalid signature'));

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });
  });

  describe('User Context', () => {
    it('should provide correct user context for authenticated requests', async () => {
      AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
      graphqlHelper.setAuthToken('Bearer valid-token');

      const getUserQuery = `
        query {
          getUser {
            ... on UserResponse {
              result {
                user {
                  id
                  firstName
                  email
                  role
                }
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getUserQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUser.result.user.id).toBe(testUser._id.toString());
      expect(response.body.data.getUser.result.user.firstName).toBe(testUser.firstName);
      expect(response.body.data.getUser.result.user.email).toBe(testUser.email);
    });

    it('should handle user not found in database', async () => {
      // Mock token verification for non-existent user
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockResolvedValue({
        sub: 'non-existent-user-id',
        metadata: { role: ['user'] }
      });

      graphqlHelper.setAuthToken('Bearer valid-token');

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });
  });

  describe('Role-Based Authorization', () => {
    it('should allow admin users to access admin-only operations', async () => {
      AuthTestUtils.mockClerkVerification(adminUser.externalId, adminUser.role);
      graphqlHelper.setAuthToken('Bearer admin-token');

      const adminQuery = `
        query {
          getUsers {
            ... on UsersResponse {
              status
              result {
                users {
                  id
                  firstName
                  role
                }
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(adminQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUsers.status).toBe('SUCCESS');
      expect(response.body.data.getUsers.result.users.length).toBeGreaterThan(0);
    });

    it('should allow users to update their own profile', async () => {
      AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
      graphqlHelper.setAuthToken('Bearer user-token');

      const updateUserMutation = `
        mutation {
          updateUser(input: { firstName: "Updated" }) {
            ... on UserResponse {
              status
              result {
                user {
                  firstName
                }
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.mutation(updateUserMutation);

      expect(response.status).toBe(200);
      expect(response.body.data.updateUser.status).toBe('SUCCESS');
      expect(response.body.data.updateUser.result.user.firstName).toBe('Updated');
    });

    it('should prevent regular users from updating other users roles', async () => {
      AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
      graphqlHelper.setAuthToken('Bearer user-token');

      const updateRoleMutation = `
        mutation {
          updateUserRole(input: { userId: "${adminUser._id}", role: ["super_admin"] }) {
            ... on UserResponse {
              status
            }
            ... on UserErrorResponse {
              status
              message
            }
          }
        }
      `;

      const response = await graphqlHelper.mutation(updateRoleMutation);

      expect(response.status).toBe(200);
      // Should either fail with authorization error or return FAILURE status
      if (response.body.data.updateUserRole) {
        expect(response.body.data.updateUserRole.status).toBe('FAILURE');
      } else {
        expect(response.body.errors).toBeDefined();
      }
    });
  });

  describe('Clerk Integration', () => {
    it('should verify user with Clerk service', async () => {
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockResolvedValue({
        sub: testUser.externalId,
        metadata: { role: testUser.role }
      });
      clerkUtils.verifyClerkUser.mockResolvedValue(true);

      graphqlHelper.setAuthToken('Bearer valid-token');

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUser.status).toBe('SUCCESS');
      expect(clerkUtils.verifyClerkUser).toHaveBeenCalledWith(testUser.externalId);
    });

    it('should handle Clerk service unavailable', async () => {
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockResolvedValue({
        sub: testUser.externalId,
        metadata: { role: testUser.role }
      });
      clerkUtils.verifyClerkUser.mockRejectedValue(new Error('Clerk service unavailable'));

      graphqlHelper.setAuthToken('Bearer valid-token');

      const protectedQuery = `
        query {
          getUser {
            ... on UserResponse {
              status
            }
          }
        }
      `;

      const response = await graphqlHelper.query(protectedQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });
  });

  describe('Authorization Header Formats', () => {
    it('should accept Bearer token format', async () => {
      AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
      graphqlHelper.setAuthToken('Bearer valid-token');

      const response = await graphqlHelper.query('query { getUser { ... on UserResponse { status } } }');
      expect(response.status).toBe(200);
      expect(response.body.data.getUser.status).toBe('SUCCESS');
    });

    it('should reject non-Bearer token format', async () => {
      graphqlHelper.setAuthToken('Basic dXNlcjpwYXNz'); // Basic auth format

      const response = await graphqlHelper.query('query { getUser { ... on UserResponse { status } } }');
      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });

    it('should reject malformed authorization header', async () => {
      graphqlHelper.setAuthToken('InvalidFormat');

      const response = await graphqlHelper.query('query { getUser { ... on UserResponse { status } } }');
      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });
  });
});
