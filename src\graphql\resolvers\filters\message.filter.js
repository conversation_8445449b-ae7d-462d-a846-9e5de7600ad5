const { isValidObjectId, createSafeRegexQuery } = require("../../../utils/validation.util");

const buildMessageQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.id) {
            if (Array.isArray(filter.id)) {
                query._id = { $in: filter.id };
            } else {
                if (isValidObjectId(filter.id)) {
                    query._id = filter.id;
                } else {
                    throw new Error('Invalid message ID format');
                }
            }
        }

        if (filter.type) {
            query.type = filter.type;
        }

        if (filter.text) {
            const regexQuery = createSafeRegexQuery(filter.text);
            if (regexQuery) {
                query.text = regexQuery;
            }
        }

        if (filter.parentMessageId) {
            if (isValidObjectId(filter.parentMessageId)) {
                query.parentMessageId = filter.parentMessageId;
            } else {
                throw new Error('Invalid parent message ID format');
            }
        }
    }

    return query;
}

module.exports = buildMessageQuery;
