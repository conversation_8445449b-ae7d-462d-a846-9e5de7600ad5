const { isValidObjectId } = require('mongoose');
const { User } = require('../../models/User');
const VenueAddress = require('../../models/VenueAddress');
const buildUserQuery = require('../resolvers/filters/user.filter');
const buildVenueAddressQuery = require('../resolvers/filters/venueAddress.filter');

const buildAddressBookQuery = async (filters) => {
    const query = {};

    if (!filters) return query;

    if (filters.userId) {
        if (isValidObjectId(filters.userId)) {
            query.user = filters.userId;
        } else {
            throw new Error('Invalid userId provided');
        }
    }
    
    if (filters.user) {
        if (typeof filters.user === 'string') {
            if (isValidObjectId(filters.user)) {
                query.user = filters.user;
            } else {
                throw new Error('Invalid user ID provided');
            }
        } else if (typeof filters.user === 'object') {
            const userQuery = await buildUserQuery(filters.user);
            if (Object.keys(userQuery).length > 0) {
                const matchingUsers = await User.find(userQuery).select('_id');
                if (matchingUsers.length > 0) {
                    query.user = { $in: matchingUsers.map(user => user._id) };
                } else {
                    query.user = { $in: [] };
                }
            }
        }
    }

    if (filters.venueAddressId) {
        if (isValidObjectId(filters.venueAddressId)) {
            query.venueAddress = filters.venueAddressId;
        } else {
            throw new Error('Invalid venueAddressId provided');
        }
    }
    
    if (filters.venueAddress) {
        if (typeof filters.venueAddress === 'string') {
            if (isValidObjectId(filters.venueAddress)) {
                query.venueAddress = filters.venueAddress;
            } else {
                throw new Error('Invalid venueAddress ID provided');
            }
        } else if (typeof filters.venueAddress === 'object') {
            const venueAddressQuery = buildVenueAddressQuery(filters.venueAddress);
            if (Object.keys(venueAddressQuery).length > 0) {
                const matchingAddresses = await VenueAddress.find(venueAddressQuery).select('_id');
                if (matchingAddresses.length > 0) {
                    query.venueAddress = { $in: matchingAddresses.map(addr => addr._id) };
                } else {
                    query.venueAddress = { $in: [] };
                }
            }
        }
    }

    if (filters.label) {
        query.label = { $regex: new RegExp(filters.label, 'i') };
    }

    return query;
};

module.exports = buildAddressBookQuery; 