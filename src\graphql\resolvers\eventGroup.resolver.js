const { createResponse } = require("../../utils/response.util");
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { validateReferences } = require('../../utils/validation.util');
const buildEventGroupFilter = require('./filters/eventGroup.filter');
const buildEventGroupMemberFilter = require('./filters/eventGroupMember.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const EventGroup = require("../../models/EventGroup");
const MdInterest = require("../../models/MdInterest");
const MdInterestCategory = require("../../models/MdInterestCategory");
const { User } = require("../../models/User");
const { notifyInviteToCircle, notifyCirclePhotoUpdated, notifyCircleInterestsChanged, notifyCircleTitleChanged, notifyCircleDescriptionChanged } = require("../../notification/circleNotification");
const { hasEventGroupMemberLevelAccess, hasEventGroupOrganizerLevelAccess, hasEventGroupCoOrganizerLevelAccess } = require('../../utils/auth/accessLevels.util');
const EventGroupInvitation = require("../../models/EventGroupInvitation");
const mongoose = require('mongoose');

const eventGroupSchema = {
    interests: { type: 'array', model: MdInterest },
    interestCategories: { type: 'array', model: MdInterestCategory },
    parentGroup: { type: 'single', model: EventGroup },
}

const processMembers = async (members) => {
    if (!members || members.length === 0) {
        return [];
    }

    const memberIds = [];
    
    for (const member of members) {
        const queryConditions = [];
        if (member.email) {
            queryConditions.push({ email: member.email });
        }
        if (member.phone) {
            queryConditions.push({ phone: member.phone });
        }

        let user = queryConditions.length > 0 
            ? await User.findOne({ $or: queryConditions })
            : null;

        if (!user) {
            const newUser = new User({
                role: ["user"],
                firstName: member.firstName,
                lastName: member.lastName,
                email: member.email,
                phone: member.phone,
                verified: false
            });
            await newUser.save();
            user = newUser;
        }

        memberIds.push(user._id);
    }

    return memberIds;
}

const eventGroupResolvers = {
    EventGroup: {
        interests: async (parent) => {
            try {
                return await MdInterest.find({ _id: { $in: parent.interests } });
            } catch (error) {
                console.error('Error fetching interests:', error);
                throw new Error('Failed to fetch interests');
            }
        },

        interestCategories: async (parent) => {
            try {
                return await MdInterestCategory.find({ _id: { $in: parent.interestCategories } });
            } catch (error) {
                console.error('Error fetching interest categories:', error);
                throw new Error('Failed to fetch interest categories');
            }
        },

        members: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.members } });
            } catch (error) {
                console.error('Error fetching members:', error);
                throw new Error('Failed to fetch members');
            }
        },

        membersCount: async (parent) => {
            try {
                const membersArray = parent.members || [];
                const organizersArray = parent.organizers || [];
                return membersArray.length + organizersArray.length - 1;
            } catch (error) {
                console.error('Error calculating members count:', error);
                return 0;
            }
        },

        organizers: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.organizers } });
            } catch (error) {
                console.error('Error fetching organizers:', error);
                throw new Error('Failed to fetch organizers');
            }
        },

        parentGroup: async (parent) => {
            try {
                return await EventGroup.findById(parent.parentGroup);
            } catch (error) {
                console.error('Error fetching parent group:', error);
                throw new Error('Failed to fetch parent group');
            }
        },

        createdBy: async (parent) => {
            try {
                return await User.findById(parent.createdBy);
            } catch (error) {
                console.error('Error fetching created by:', error);
                throw new Error('Failed to fetch created by');
            }
        },

        userRole: async (parent, _, context) => {
            try {
                const user = await User.findById(context.user._id);
                if (!user) {
                    throw new Error('User not found');
                }
                
                let role = 'MEMBER';

                const isOrganizer = parent.organizers.some(organizer => organizer.id.toString() === user.id);
                const isCreator = parent.createdBy.id === user.id;

                if (isCreator && isOrganizer) {
                    role = 'ORGANIZER';
                }
                else if (isOrganizer) {
                    role = 'CO_ORGANIZER';
                }
                
                return role;
            } catch (error) {
                console.error('Error fetching user role:', error);
                throw new Error('Failed to fetch user role');
            }
        },

        invitedMembers: async (parent) => {
            try {
                return await User.find({ _id: { $in: parent.invitedMembers } });
            } catch (error) {
                console.error('Error fetching invited members:', error);
                throw new Error('Failed to fetch invited members');
            }
        }
        
    },
    
    Query: {
        getEventGroupById: async (_, { id }, context) => {
            try{
                // Check access level
                const hasAccess = await hasEventGroupMemberLevelAccess(context.user._id, id);
                if (!hasAccess) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Access denied', {
                        errors: [{ field: 'id', message: 'You do not have permission to access this event group' }]
                    });
                }

                let eventGroup = await getByIdCache(id);
                if (!eventGroup) {
                    eventGroup = await EventGroup.findById(id)
                        .populate('invitedMembers')
                        .populate('members')
                        .populate('organizers')
                        .populate('createdBy');
                    
                    if (!eventGroup) {
                        return createResponse('EventGroupErrorResponse', 'FAILURE', 'Event group not found', {
                            errors: [{ field: 'id', message: 'Event group not found' }]
                        });
                    }
                    await setCache(id, eventGroup);
                } else {
                    eventGroup = await EventGroup.findById(id)
                        .populate('invitedMembers')
                        .populate('members')
                        .populate('organizers')
                        .populate('createdBy');
                }
                return createResponse('EventGroupResponse', 'SUCCESS', 'Event group fetched successfully', {
                    result: { eventGroup }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Error getting event group by id', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        },

        getEventGroups: async (_, { filter, pagination }, context) => {
            try {
                const query = await buildEventGroupFilter(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const userId = context.user._id;
                
                const accessibleGroups = await EventGroup.find({
                    $or: [
                        { members: userId },
                        { organizers: userId },
                        { createdBy: userId }
                    ]
                }).select('_id');

                if (accessibleGroups.length === 0) {
                    return createResponse('EventGroupsResponse', 'FAILURE', 'No accessible event groups found', {
                        result: { eventGroups: [] },
                        pagination: { totalItems: 0, totalPages: 0, currentPage: 1, pageSize: limit, skip }
                    });
                }

                // Add accessible group IDs to the query
                query._id = { $in: accessibleGroups.map(group => group._id) };

                const eventGroups = await EventGroup.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                const paginationInfo = await getPaginationInfo(EventGroup, query, limit, skip);

                if (eventGroups.length === 0) {
                    return createResponse('EventGroupsResponse', 'FAILURE', 'No event groups found', {
                        result: { eventGroups },
                        pagination: paginationInfo
                    });
                }

                return createResponse('EventGroupsResponse', 'SUCCESS', 'Event groups fetched successfully', {
                    result: { eventGroups },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Error getting event groups', {
                    errors: [{ field: 'filter', message: error.message }]
                });
            }
        },

        getEventGroupMembers: async (_, { eventGroupId, filter, pagination }, { user }) => {
            try {
                // Check if user has access to view event group members
                const hasAccess = await hasEventGroupMemberLevelAccess(user.id, eventGroupId);
                if (!hasAccess) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'You do not have permission to view event group members', {
                        errors: [{ message: 'Access denied' }]
                    });
                }

                // Get the event group
                const eventGroup = await EventGroup.findById(eventGroupId);
                if (!eventGroup) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Event group not found', {
                        errors: [{ message: 'Event group not found' }]
                    });
                }

                // Get all users (members and organizers)
                const users = await User.find({
                    _id: { $in: [...eventGroup.members, ...eventGroup.organizers] }
                });

                // Create member objects with roles
                let members = users.map(user => {
                    const isOrganizer = eventGroup.organizers.includes(user._id.toString());
                    const isCreator = eventGroup.createdBy.toString() === user._id.toString();
                    
                    let role = 'MEMBER';
                    if (isCreator && isOrganizer) {
                        role = 'ORGANIZER';
                    } else if (isOrganizer) {
                        role = 'CO_ORGANIZER';
                    }

                    return {
                        user,
                        role
                    };
                });

                // Apply filters if provided
                if (filter) {
                    const filterQuery = await buildEventGroupMemberFilter(filter);
                    
                    if (filterQuery.role) {
                        members = members.filter(member => 
                            member.role.match(new RegExp(filterQuery.role.$regex, 'i'))
                        );
                    }

                    if (filterQuery.user) {
                        members = members.filter(member => {
                            const user = member.user;
                            let matches = true;

                            if (filterQuery.user.firstName) {
                                matches = matches && user.firstName.match(new RegExp(filterQuery.user.firstName.$regex, 'i'));
                            }
                            if (filterQuery.user.lastName) {
                                matches = matches && user.lastName?.match(new RegExp(filterQuery.user.lastName.$regex, 'i'));
                            }
                            if (filterQuery.user.email) {
                                matches = matches && user.email?.match(new RegExp(filterQuery.user.email.$regex, 'i'));
                            }
                            if (filterQuery.user.phone) {
                                matches = matches && user.phone.match(new RegExp(filterQuery.user.phone.$regex, 'i'));
                            }

                            return matches;
                        });
                    }
                }

                // Sort members alphabetically by first name, then last name
                members.sort((a, b) => {
                    const nameA = `${a.user.firstName} ${a.user.lastName || ''}`.toLowerCase();
                    const nameB = `${b.user.firstName} ${b.user.lastName || ''}`.toLowerCase();
                    return nameA.localeCompare(nameB);
                });

                const pageSize = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
                const paginatedMembers = members.slice(skip, skip + pageSize);

                const totalItems = members.length;
                const totalPages = Math.ceil(totalItems / pageSize);
                const currentPage = Math.floor(skip / pageSize) + 1;
                const hasMore = skip + pageSize < totalItems;

                const paginationInfo = {
                    totalItems,
                    totalPages,
                    currentPage,
                    pageSize,
                    skip,
                    hasMore
                };

                return createResponse('EventGroupMembersResponse', 'SUCCESS', 'Event group members retrieved successfully', {
                    result: paginatedMembers,
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error('Error in getEventGroupMembers:', error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Failed to retrieve event group members', {
                    errors: [{ message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createEventGroup: async (_, { input }, context) => {
            try {
                if (input.parentGroup) {
                    const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, input.parentGroup);
                    if (!hasAccess) {
                        return createResponse('EventGroupErrorResponse', 'FAILURE', 'Access denied', {
                            errors: [{ field: 'parentGroup', message: 'You do not have organizer level access to the parent group' }]
                        });
                    }
                }

                if (input.organizers && input.organizers.length > 2) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Too many co-organizers', {
                        errors: [{ field: 'organizers', message: 'Maximum 2 co-organizers allowed' }]
                    });
                }

                const referenceValidationErrors = await validateReferences(input, eventGroupSchema, 'EventGroup');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createEventGroup',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];
                        
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Invalid event group information', {
                        errors
                    });
                }

                const eventGroup = new EventGroup(input);
                eventGroup.organizers = [context.user._id];
                eventGroup.members = [context.user._id];  // Add creator to members list
                eventGroup.createdBy = context.user._id;

                if (input.organizers) {
                    const organizerIds = await processMembers(input.organizers);
                    const uniqueOrganizerIds = [...new Set([...organizerIds, context.user._id])];
                    eventGroup.organizers = uniqueOrganizerIds;
                    eventGroup.members = uniqueOrganizerIds;  // Add all organizers to members list
                }

                await eventGroup.save();
                await setCache(eventGroup._id, eventGroup);

                await notifyInviteToCircle(eventGroup, [], context.user._id);

                return createResponse('EventGroupResponse', 'SUCCESS', 'Event group created successfully', {
                    result: { eventGroup }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Error creating event group', {
                    errors: [{ field: 'input', message: error.message }]
                });
            }
        },

        updateEventGroup: async (_, { id, input }, context) => {
            try {
                const hasAccess = await hasEventGroupCoOrganizerLevelAccess(context.user._id, id);
                if (!hasAccess) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Access denied', {
                        errors: [{ field: 'id', message: 'You do not have co-organizer level access to this event group' }]
                    });
                }

                const validationErrors = await validateReferences(input, eventGroupSchema, 'EventGroup');
                if (validationErrors) {
                    const errors = Array.isArray(validationErrors) 
                        ? validationErrors 
                        : [{ 
                            field: validationErrors.field || 'updateEventGroup',
                            message: validationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Invalid event group information', {
                        errors
                    });
                }

                const existingEventGroup = await EventGroup.findById(id);
                if (!existingEventGroup) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Event group not found', {
                        errors: [{ field: 'id', message: 'Event group not found' }]
                    });
                }

                const updatedInput = { ...input };

                if (input.members) {
                    const memberIds = await processMembers(input.members);
                    const newMemberIds = memberIds.map(m => m.toString());
                    
                    let updatedMembers = [...newMemberIds];
                    
                    const creatorId = existingEventGroup.createdBy.toString();
                    if (!updatedMembers.includes(creatorId)) {
                        updatedMembers.push(creatorId);
                    }

                    const removedMembers = existingEventGroup.members
                        .map(m => m.toString())
                        .filter(memberId => !updatedMembers.includes(memberId));

                    if (removedMembers.length > 0) {
                        await EventGroupInvitation.deleteMany({
                            eventGroup: id,
                            user: { $in: removedMembers }
                        });
                    }
                    
                    updatedInput.members = updatedMembers;
                } else {
                    updatedInput.members = [existingEventGroup.createdBy];
                }

                if (input.organizers === null || (Array.isArray(input.organizers) && input.organizers.length === 0)) {
                    updatedInput.organizers = [existingEventGroup.createdBy];
                } else if (input.organizers) {
                    const organizerIds = await processMembers(input.organizers);
                    const newOrganizerIds = organizerIds.map(m => m.toString());
                    
                    let updatedOrganizers = [...newOrganizerIds];
                    
                    const creatorId = existingEventGroup.createdBy.toString();
                    if (!updatedOrganizers.includes(creatorId)) {
                        updatedOrganizers.push(creatorId);
                    }

                    const coOrganizersCount = updatedOrganizers.filter(id => id !== creatorId).length;
                    if (coOrganizersCount > 2) {
                        return createResponse('EventGroupErrorResponse', 'FAILURE', 'Too many co-organizers', {
                            errors: [{ field: 'organizers', message: 'Maximum 2 co-organizers allowed' }]
                        });
                    }
                    
                    updatedInput.organizers = updatedOrganizers;

                    if (!updatedInput.members) {
                        updatedInput.members = existingEventGroup.members.map(m => m.toString());
                    }
                    updatedInput.members = updatedInput.members.filter(memberId => 
                        !updatedOrganizers.includes(memberId) || memberId === creatorId
                    );
                }

                const eventGroup = await EventGroup.findByIdAndUpdate(id, updatedInput, { new: true });
                await setCache(id, eventGroup);

                if (input.imageUrl)
                    await notifyCirclePhotoUpdated(eventGroup, context.user._id);

                if ((input.interests != null && input.interests.length > 0) || 
                    (input.interestCategories != null && input.interestCategories.length > 0))
                    await notifyCircleInterestsChanged(eventGroup, context.user._id);

                if (input.name)
                    await notifyCircleTitleChanged(existingEventGroup, eventGroup, context.user._id);

                if (input.description)
                    await notifyCircleDescriptionChanged(eventGroup, context.user._id);

                return createResponse('EventGroupResponse', 'SUCCESS', 'Event group updated successfully', {
                    result: { eventGroup }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Error updating event group', {
                    errors: [{ field: 'input', message: error.message }]
                });
            }
        },

        deleteEventGroup: async (_, { id }, context) => {
            try {
                const hasAccess = await hasEventGroupOrganizerLevelAccess(context.user._id, id);
                if (!hasAccess) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Access denied', {
                        errors: [{ field: 'id', message: 'You do not have organizer level access to this event group' }]
                    });
                }

                const eventGroup = await EventGroup.findById(id);
                if (!eventGroup) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Event group not found', {
                        errors: [{ field: 'id', message: 'Event group not found' }]
                    });
                }

                const references = await findReferences(id, 'EventGroup');
                if (references.length > 0) {
                    return createResponse('EventGroupErrorResponse', 'FAILURE', 'Event group has references', {
                        errors: [{ field: 'id', message: `Event group has references: ${references.join(', ')}` }]
                    });
                }
                
                await EventGroup.findByIdAndDelete(id);
                await clearCacheById(id);

                return createResponse('EventGroupResponse', 'SUCCESS', 'Event group deleted successfully', {
                    result: { eventGroup }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventGroupErrorResponse', 'FAILURE', 'Error deleting event group', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        }
    }
}

module.exports = eventGroupResolvers;