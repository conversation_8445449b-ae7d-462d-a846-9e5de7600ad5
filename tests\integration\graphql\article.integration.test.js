const { Graph<PERSON>Test<PERSON>el<PERSON>, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');
const axios = require('axios');

// Mock axios for external API calls
jest.mock('axios');
const mockedAxios = axios;

describe('Article GraphQL Integration Tests', () => {
  let graphqlHelper;
  let testUser;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
    
    // Reset axios mocks
    jest.clearAllMocks();
  });

  describe('Article Queries', () => {
    it('should get articles from external API successfully', async () => {
      // Mock external API response
      const mockArticles = [
        {
          id: '1',
          headline: 'Test Article 1',
          description: 'Description for test article 1',
          image: 'https://example.com/image1.jpg',
          source: {
            link: 'https://example.com/article1',
            mainUrl: 'https://example.com'
          },
          date: '2024-01-01T00:00:00Z',
          approved: true
        },
        {
          id: '2',
          headline: 'Test Article 2',
          description: 'Description for test article 2',
          image: 'https://example.com/image2.jpg',
          source: {
            link: 'https://example.com/article2',
            mainUrl: 'https://example.com'
          },
          date: '2024-01-02T00:00:00Z',
          approved: true
        }
      ];

      mockedAxios.post.mockResolvedValue({
        data: mockArticles
      });

      const getArticlesQuery = `
        query GetArticles($filter: ArticleFilter, $pagination: PaginationInput) {
          getArticles(filter: $filter, pagination: $pagination) {
            ... on ArticlesResponse {
              status
              message
              result {
                articles {
                  id
                  headline
                  description
                  image
                  source {
                    link
                    mainUrl
                  }
                  date
                  approved
                }
              }
              pagination {
                totalItems
                totalPages
                currentPage
                pageSize
              }
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          approved: true
        },
        pagination: {
          limit: 10,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getArticlesQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getArticles.status).toBe('SUCCESS');
      expect(response.body.data.getArticles.result.articles).toHaveLength(2);
      expect(response.body.data.getArticles.result.articles[0].headline).toBe('Test Article 1');
      expect(response.body.data.getArticles.pagination.totalItems).toBe(2);
    });

    it('should handle empty articles response from external API', async () => {
      // Mock empty response
      mockedAxios.post.mockResolvedValue({
        data: []
      });

      const getArticlesQuery = `
        query GetArticles {
          getArticles {
            ... on ArticlesResponse {
              status
              message
              result {
                articles {
                  id
                  headline
                }
              }
              pagination {
                totalItems
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getArticlesQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getArticles.status).toBe('SUCCESS');
      expect(response.body.data.getArticles.result.articles).toHaveLength(0);
      expect(response.body.data.getArticles.pagination.totalItems).toBe(0);
    });

    it('should handle external API failure gracefully', async () => {
      // Mock API failure
      mockedAxios.post.mockRejectedValue(new Error('External API unavailable'));

      const getArticlesQuery = `
        query GetArticles {
          getArticles {
            ... on ArticlesResponse {
              status
              message
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getArticlesQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getArticles.status).toBe('FAILURE');
    });

    it('should get all articles with filters', async () => {
      const mockArticles = [
        {
          id: '1',
          headline: 'Birthday Party Ideas',
          description: 'Creative ideas for birthday celebrations',
          image: 'https://example.com/birthday.jpg',
          source: {
            link: 'https://example.com/birthday',
            mainUrl: 'https://example.com'
          },
          date: '2024-01-01T00:00:00Z',
          approved: true
        }
      ];

      mockedAxios.post.mockResolvedValue({
        data: mockArticles
      });

      const getAllArticlesQuery = `
        query GetAllArticles($filter: GetAllArticlesFilter, $pagination: GetAllArticlesPagination) {
          getAllArticles(filter: $filter, pagination: $pagination) {
            ... on ArticlesResponse {
              status
              message
              result {
                articles {
                  id
                  headline
                  description
                  approved
                }
              }
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          approved: true,
          search: 'birthday',
          sort_by: 'date',
          sort_order: -1
        },
        pagination: {
          limit: 5,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getAllArticlesQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getAllArticles.status).toBe('SUCCESS');
      expect(response.body.data.getAllArticles.result.articles).toHaveLength(1);
      expect(response.body.data.getAllArticles.result.articles[0].headline).toContain('Birthday');
    });
  });

  describe('Article Mutations', () => {
    it('should create a new article successfully', async () => {
      const createArticleMutation = `
        mutation CreateArticle($input: CreateArticleInput!) {
          createArticle(input: $input) {
            ... on ArticleResponse {
              status
              message
              result {
                article {
                  id
                  headline
                  description
                  image
                  source {
                    link
                    mainUrl
                  }
                  date
                  approved
                }
              }
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          headline: 'New Party Planning Article',
          description: 'A comprehensive guide to planning the perfect party',
          image: 'https://example.com/party-guide.jpg',
          source: {
            link: 'https://example.com/party-planning-guide',
            mainUrl: 'https://example.com'
          },
          date: new Date().toISOString(),
          approved: false
        }
      };

      const response = await graphqlHelper.mutation(createArticleMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createArticle.status).toBe('SUCCESS');
      expect(response.body.data.createArticle.result.article.headline).toBe('New Party Planning Article');
      expect(response.body.data.createArticle.result.article.approved).toBe(false);
    });

    it('should handle article creation with invalid data', async () => {
      const createArticleMutation = `
        mutation CreateArticle($input: CreateArticleInput!) {
          createArticle(input: $input) {
            ... on ArticleResponse {
              status
              message
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          // Missing required fields
          description: 'Incomplete article'
        }
      };

      const response = await graphqlHelper.mutation(createArticleMutation, variables);

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });

    it('should update an existing article successfully', async () => {
      // First create an article
      const Article = require('../../../src/models/Article');
      const article = new Article({
        headline: 'Original Headline',
        description: 'Original description',
        image: 'https://example.com/original.jpg',
        source: {
          link: 'https://example.com/original',
          mainUrl: 'https://example.com'
        },
        date: new Date(),
        approved: false
      });
      await article.save();

      const updateArticleMutation = `
        mutation UpdateArticle($id: ID!, $input: UpdateArticleInput!) {
          updateArticle(id: $id, input: $input) {
            ... on ArticleResponse {
              status
              message
              result {
                article {
                  id
                  headline
                  description
                  approved
                }
              }
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: article._id.toString(),
        input: {
          headline: 'Updated Headline',
          description: 'Updated description',
          approved: true
        }
      };

      const response = await graphqlHelper.mutation(updateArticleMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateArticle.status).toBe('SUCCESS');
      expect(response.body.data.updateArticle.result.article.headline).toBe('Updated Headline');
      expect(response.body.data.updateArticle.result.article.approved).toBe(true);
    });

    it('should delete an article successfully', async () => {
      // First create an article
      const Article = require('../../../src/models/Article');
      const article = new Article({
        headline: 'Article to Delete',
        description: 'This article will be deleted',
        image: 'https://example.com/delete.jpg',
        source: {
          link: 'https://example.com/delete',
          mainUrl: 'https://example.com'
        },
        date: new Date(),
        approved: false
      });
      await article.save();

      const deleteArticleMutation = `
        mutation DeleteArticle($id: ID!) {
          deleteArticle(id: $id) {
            ... on ArticleResponse {
              status
              message
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: article._id.toString()
      };

      const response = await graphqlHelper.mutation(deleteArticleMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.deleteArticle.status).toBe('SUCCESS');

      // Verify article is deleted from database
      const deletedArticle = await Article.findById(article._id);
      expect(deletedArticle).toBeNull();
    });

    it('should handle deletion of non-existent article', async () => {
      const deleteArticleMutation = `
        mutation DeleteArticle($id: ID!) {
          deleteArticle(id: $id) {
            ... on ArticleResponse {
              status
              message
            }
            ... on ArticleErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: '507f1f77bcf86cd799439011' // Non-existent article ID
      };

      const response = await graphqlHelper.mutation(deleteArticleMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.deleteArticle.status).toBe('FAILURE');
    });
  });

  describe('Article Filtering and Search', () => {
    beforeEach(() => {
      // Mock articles with different properties for filtering tests
      const mockArticles = [
        {
          id: '1',
          headline: 'Birthday Party Decorations',
          description: 'Beautiful decoration ideas for birthday parties',
          approved: true,
          date: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          headline: 'Wedding Planning Guide',
          description: 'Complete guide for planning your dream wedding',
          approved: false,
          date: '2024-01-02T00:00:00Z'
        },
        {
          id: '3',
          headline: 'Corporate Event Management',
          description: 'Tips for managing successful corporate events',
          approved: true,
          date: '2024-01-03T00:00:00Z'
        }
      ];

      mockedAxios.post.mockResolvedValue({
        data: mockArticles
      });
    });

    it('should filter articles by search term', async () => {
      const getArticlesQuery = `
        query GetArticles($filter: ArticleFilter) {
          getArticles(filter: $filter) {
            ... on ArticlesResponse {
              result {
                articles {
                  headline
                  description
                }
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          search: 'birthday'
        }
      };

      const response = await graphqlHelper.query(getArticlesQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getArticles.result.articles).toHaveLength(3);
      // Note: Actual filtering would happen in the resolver implementation
    });

    it('should filter articles by date range', async () => {
      const getArticlesQuery = `
        query GetArticles($filter: ArticleFilter) {
          getArticles(filter: $filter) {
            ... on ArticlesResponse {
              result {
                articles {
                  headline
                  date
                }
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          dateRange: {
            start: '2024-01-01T00:00:00Z',
            end: '2024-01-02T23:59:59Z'
          }
        }
      };

      const response = await graphqlHelper.query(getArticlesQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getArticles.result.articles).toHaveLength(3);
    });
  });
});
