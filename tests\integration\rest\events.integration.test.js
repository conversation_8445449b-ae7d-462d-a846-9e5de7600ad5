const request = require('supertest');
const { TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Events SSE REST API Integration Tests', () => {
  let app;
  let testUser;
  let testEvent;
  let testParty;

  beforeAll(() => {
    app = global.testApp;
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    testEvent = await TestDataFactory.createEvent(testUser);
    testParty = await TestDataFactory.createParty(testEvent);
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('GET /activity_events', () => {
    it('should establish SSE connection successfully with valid authentication', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .timeout(5000);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toBe('text/event-stream');
      expect(response.headers['cache-control']).toBe('no-cache');
      expect(response.headers['connection']).toBe('keep-alive');
    });

    it('should reject connection without authentication', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });

    it('should reject connection with invalid party ID', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: '507f1f77bcf86cd799439011' }) // Non-existent party ID
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Party not found');
    });

    it('should reject connection without party access', async () => {
      // Create another user and party that testUser doesn't have access to
      const otherUser = await TestDataFactory.createUser({
        externalId: 'other-user-id',
        email: '<EMAIL>'
      });
      const otherEvent = await TestDataFactory.createEvent(otherUser);
      const otherParty = await TestDataFactory.createParty(otherEvent);

      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: otherParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Unauthorized access to this party');
    });

    it('should handle missing partyId parameter', async () => {
      const response = await request(app)
        .get('/activity_events')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(404);
    });
  });

  describe('SSE Connection Behavior', () => {
    it('should send initial connection message', (done) => {
      const req = request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .buffer(false)
        .parse((res, callback) => {
          let data = '';
          res.on('data', (chunk) => {
            data += chunk.toString();
            
            // Check for initial connection message
            if (data.includes('Connected to event stream')) {
              const lines = data.split('\n');
              const dataLine = lines.find(line => line.startsWith('data: '));
              
              if (dataLine) {
                const messageData = JSON.parse(dataLine.substring(6));
                expect(messageData.type).toBe('connection');
                expect(messageData.message).toBe('Connected to event stream');
                callback(null, data);
                done();
              }
            }
          });
          
          res.on('error', (err) => {
            callback(err);
            done(err);
          });
        });

      req.end();
    });

    it('should handle connection timeout gracefully', (done) => {
      const req = request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .timeout(2000)
        .buffer(false);

      req.on('timeout', () => {
        // This is expected behavior for SSE connections
        done();
      });

      req.on('error', (err) => {
        if (err.code === 'ECONNABORTED' && err.timeout) {
          // Expected timeout
          done();
        } else {
          done(err);
        }
      });

      req.end();
    });
  });

  describe('Authentication Edge Cases', () => {
    it('should handle invalid JWT token', async () => {
      // Mock invalid token verification
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockRejectedValue(new Error('Invalid token'));

      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer invalid-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication failed');
    });

    it('should handle malformed authorization header', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'InvalidFormat token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });

    it('should handle user not found in database', async () => {
      // Mock token verification but user not in DB
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockResolvedValue({
        sub: 'non-existent-user-id',
        metadata: { role: ['user'] }
      });

      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('User not found in database');
    });
  });

  describe('CORS Headers', () => {
    it('should include proper CORS headers in SSE response', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .set('Origin', 'https://example.com')
        .timeout(1000);

      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toBe('GET');
      expect(response.headers['access-control-allow-headers']).toContain('Authorization');
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      const Party = require('../../../src/models/Party');
      jest.spyOn(Party, 'findById').mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: testParty._id.toString() })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Failed to set up SSE connection');
    });

    it('should handle invalid MongoDB ObjectId format', async () => {
      const response = await request(app)
        .get('/activity_events')
        .query({ partyId: 'invalid-object-id' })
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Failed to set up SSE connection');
    });
  });
});
