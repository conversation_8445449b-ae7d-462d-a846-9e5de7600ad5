const { Graph<PERSON><PERSON><PERSON><PERSON>el<PERSON>, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Task GraphQL Integration Tests', () => {
  let graphqlHelper;
  let testUser;
  let testEvent;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    testEvent = await TestDataFactory.createEvent(testUser);
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('Task Creation', () => {
    it('should create a new task successfully', async () => {
      const createTaskMutation = `
        mutation CreateTask($input: TaskInput!) {
          createTask(input: $input) {
            ... on TaskResponse {
              status
              message
              result {
                task {
                  id
                  title
                  description
                  status
                  priority
                  dueDate
                  assignee {
                    id
                    firstName
                  }
                  event {
                    id
                    name
                  }
                }
              }
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          title: 'Plan decorations',
          description: 'Choose and arrange decorations for the party',
          assignee: testUser._id.toString(),
          event: testEvent._id.toString(),
          status: 'PENDING',
          priority: 'HIGH',
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      };

      const response = await graphqlHelper.mutation(createTaskMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createTask.status).toBe('SUCCESS');
      expect(response.body.data.createTask.result.task.title).toBe('Plan decorations');
      expect(response.body.data.createTask.result.task.priority).toBe('HIGH');
      expect(response.body.data.createTask.result.task.status).toBe('PENDING');
    });

    it('should handle task creation with missing required fields', async () => {
      const createTaskMutation = `
        mutation CreateTask($input: TaskInput!) {
          createTask(input: $input) {
            ... on TaskResponse {
              status
              message
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          // Missing required fields like title, assignee, event
          description: 'Incomplete task'
        }
      };

      const response = await graphqlHelper.mutation(createTaskMutation, variables);

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Task Queries', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await TestDataFactory.createTask(testEvent, testUser);
    });

    it('should get task by ID', async () => {
      const getTaskQuery = `
        query GetTaskById($id: ID!) {
          getTaskById(id: $id) {
            ... on TaskResponse {
              status
              message
              result {
                task {
                  id
                  title
                  description
                  status
                  priority
                  dueDate
                  assignee {
                    id
                    firstName
                    email
                  }
                  event {
                    id
                    name
                  }
                  comments {
                    id
                    content
                    author {
                      id
                      firstName
                    }
                  }
                  attachments {
                    id
                    fileName
                    fileUrl
                  }
                }
              }
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testTask._id.toString()
      };

      const response = await graphqlHelper.query(getTaskQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getTaskById.status).toBe('SUCCESS');
      expect(response.body.data.getTaskById.result.task.id).toBe(testTask._id.toString());
      expect(response.body.data.getTaskById.result.task.title).toBe(testTask.title);
    });

    it('should get tasks with filters and pagination', async () => {
      // Create additional tasks
      await TestDataFactory.createTask(testEvent, testUser, { 
        title: 'Book venue',
        priority: 'HIGH',
        status: 'IN_PROGRESS'
      });
      await TestDataFactory.createTask(testEvent, testUser, { 
        title: 'Send invitations',
        priority: 'MEDIUM',
        status: 'COMPLETED'
      });

      const getTasksQuery = `
        query GetTasks($pagination: PaginationInput, $filter: TaskFilter) {
          getTasks(pagination: $pagination, filter: $filter) {
            ... on TasksResponse {
              status
              message
              result {
                tasks {
                  id
                  title
                  status
                  priority
                }
              }
              pagination {
                totalItems
                totalPages
                currentPage
                pageSize
              }
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          event: testEvent._id.toString(),
          status: ['PENDING', 'IN_PROGRESS']
        },
        pagination: {
          limit: 10,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getTasksQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getTasks.status).toBe('SUCCESS');
      expect(response.body.data.getTasks.result.tasks.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Task Updates', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await TestDataFactory.createTask(testEvent, testUser);
    });

    it('should update task successfully', async () => {
      const updateTaskMutation = `
        mutation UpdateTask($id: ID!, $input: TaskUpdateInput!) {
          updateTask(id: $id, input: $input) {
            ... on TaskResponse {
              status
              message
              result {
                task {
                  id
                  title
                  description
                  status
                  priority
                }
              }
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testTask._id.toString(),
        input: {
          title: 'Updated Task Title',
          description: 'Updated task description',
          status: 'IN_PROGRESS',
          priority: 'LOW'
        }
      };

      const response = await graphqlHelper.mutation(updateTaskMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateTask.status).toBe('SUCCESS');
      expect(response.body.data.updateTask.result.task.title).toBe('Updated Task Title');
      expect(response.body.data.updateTask.result.task.status).toBe('IN_PROGRESS');
      expect(response.body.data.updateTask.result.task.priority).toBe('LOW');
    });
  });

  describe('Task Comments', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await TestDataFactory.createTask(testEvent, testUser);
    });

    it('should create task comment successfully', async () => {
      const createCommentMutation = `
        mutation CreateTaskComment($taskId: ID!, $input: CommentInput!) {
          createTaskComment(taskId: $taskId, input: $input) {
            ... on CommentResponse {
              status
              message
              result {
                comment {
                  id
                  content
                  author {
                    id
                    firstName
                  }
                }
              }
            }
            ... on CommentErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        taskId: testTask._id.toString(),
        input: {
          content: 'This is a test comment on the task'
        }
      };

      const response = await graphqlHelper.mutation(createCommentMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createTaskComment.status).toBe('SUCCESS');
      expect(response.body.data.createTaskComment.result.comment.content).toBe('This is a test comment on the task');
    });

    it('should update task comment successfully', async () => {
      // First create a comment
      const createCommentMutation = `
        mutation CreateTaskComment($taskId: ID!, $input: CommentInput!) {
          createTaskComment(taskId: $taskId, input: $input) {
            ... on CommentResponse {
              result {
                comment {
                  id
                }
              }
            }
          }
        }
      `;

      const createResponse = await graphqlHelper.mutation(createCommentMutation, {
        taskId: testTask._id.toString(),
        input: { content: 'Original comment' }
      });

      const commentId = createResponse.body.data.createTaskComment.result.comment.id;

      // Now update the comment
      const updateCommentMutation = `
        mutation UpdateTaskComment($taskId: ID!, $commentId: ID!, $input: CommentUpdateInput!) {
          updateTaskComment(taskId: $taskId, commentId: $commentId, input: $input) {
            ... on CommentResponse {
              status
              message
              result {
                comment {
                  id
                  content
                }
              }
            }
            ... on CommentErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        taskId: testTask._id.toString(),
        commentId: commentId,
        input: {
          content: 'Updated comment content'
        }
      };

      const response = await graphqlHelper.mutation(updateCommentMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateTaskComment.status).toBe('SUCCESS');
      expect(response.body.data.updateTaskComment.result.comment.content).toBe('Updated comment content');
    });
  });

  describe('Task Deletion', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await TestDataFactory.createTask(testEvent, testUser);
    });

    it('should delete task successfully', async () => {
      const deleteTaskMutation = `
        mutation DeleteTask($id: ID!) {
          deleteTask(id: $id) {
            ... on TaskResponse {
              status
              message
            }
            ... on TaskErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testTask._id.toString()
      };

      const response = await graphqlHelper.mutation(deleteTaskMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.deleteTask.status).toBe('SUCCESS');

      // Verify task is deleted
      const getTaskQuery = `
        query GetTaskById($id: ID!) {
          getTaskById(id: $id) {
            ... on TaskResponse {
              status
            }
            ... on TaskErrorResponse {
              status
              message
            }
          }
        }
      `;

      const getResponse = await graphqlHelper.query(getTaskQuery, variables);
      expect(getResponse.body.data.getTaskById.status).toBe('FAILURE');
    });
  });
});
