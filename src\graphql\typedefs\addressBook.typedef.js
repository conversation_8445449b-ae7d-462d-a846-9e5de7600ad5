const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type AddressBook {
        id: ID!
        label: String
        user: User!
        venueAddress: VenueAddress!
        createdAt: String!
        updatedAt: String!
    }

    input AddressBookFilterInput {
        userId: ID
        user: UserFilterInput
        venueAddressId: ID
        venueAddress: VenueAddressFilterInput
        label: String
    }

    type AddressBookWrapper {
        addressBook: AddressBook!
    }

    type AddressBooksWrapper {
        addressBooks: [AddressBook]!
    }

    type AddressBookResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: AddressBookWrapper!
    }

    type AddressBooksResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: AddressBooksWrapper!
        pagination: PaginationInfo!
    }

    type AddressBookErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union AddressBookResult = AddressBookResponse | AddressBookErrorResponse
    union AddressBooksResult = AddressBooksResponse | AddressBookErrorResponse

    type Query {
        getAddressBookById(id: ID!): AddressBookResult!
        getAddressBooks(filter: AddressBookFilterInput, pagination: PaginationInput): AddressBooksResult!
    }

    input AddressBookInput {
        venueAddress: ID!
        label: String
    }

    input AddressBookUpdateInput {
        label: String
        venueAddress: ID
    }

    type Mutation {
        createAddressBook(input: AddressBookInput!): AddressBookResult!
        updateAddressBook(id: ID!, input: AddressBookUpdateInput!): AddressBookResult!
        deleteAddressBook(id: ID!): AddressBookResult!
    }
`;
