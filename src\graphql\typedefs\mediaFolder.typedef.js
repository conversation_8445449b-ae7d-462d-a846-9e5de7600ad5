const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    enum MediaFolderCategory {
        ALBUM
        COLLECTION
    }

    enum UserTypeForMediaFolder {
        HOST
        GUEST
    }

    enum UserTypeForMedia {
        GUEST
        VENDOR
    }

    input MediaFilterForMediaFolderInput {
        title: String
        userType: UserTypeForMedia
        uploadedAt: DateTimeRangeInput
        ownerId: ID
        owner: UserFilterInput
        tags: [String!]
    }

    type MediaFolder {
        id: ID!
        name: String!
        event: Event
        party: Party
        media(filter: MediaFilterForMediaFolderInput, pagination: PaginationInput): MediaFolderMediaResponse!
        owner: [User!]!
        sharedWith: [User!]
        contributors: [User!]
        publish: Boolean!
        category: MediaFolderCategory!
        tags: [Tag!]
        albumCover: [String!]
        createdAt: DateTime
        updatedAt: DateTime
    }

    type MediaFolderMediaWrapper {
        media: [Media!]!
    }

    type MediaFolderMediaResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MediaFolderMediaWrapper!
        pagination: PaginationInfo!
    }

    input MediaFolderFilterInput {
        name: String
        eventId: ID
        partyId: ID
        ownerId: ID
        partyMediaId: ID
        sharedWithId: ID
        contributorsId: ID
        event: EventFilterInput
        party: PartyFilterInput
        owner: UserFilterInput
        media: MediaFilterInput
        sharedWith: UserFilterInput
        contributors: UserFilterInput
        publish: Boolean
        category: MediaFolderCategory
        tags: TagFilterInput
        userId: ID
        userType: UserTypeForMediaFolder
    }

    type MediaFolderWrapper {
        mediaFolder: MediaFolder!
    }

    type MediaFoldersWrapper {
        mediaFolders: [MediaFolder]!
    }

    type MediaFolderResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MediaFolderWrapper!
    }

    type MediaFoldersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MediaFoldersWrapper!
        pagination: PaginationInfo!
    }

    type MediaFolderErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MediaFolderResult = MediaFolderResponse | MediaFolderErrorResponse
    union MediaFoldersResult = MediaFoldersResponse | MediaFolderErrorResponse

    type Query {
        getMediaFolderById(id: ID!): MediaFolderResult!
        getMediaFolders(filter: MediaFolderFilterInput, pagination: PaginationInput): MediaFoldersResult!
        getFavoritesMediaFolder: MediaFolderResult!
        getArchivesMediaFolder: MediaFolderResult!
    }

    input MediaFolderInput {
        name: String!
        event: ID
        party: ID
        media: [ID!]
        sharedWith: [ID!]
        contributors: [ID!]
        publish: Boolean
        category: MediaFolderCategory!
        tags: [ID!]
        albumCover: [String!]
    }

    input MediaFolderUpdateInput {
        name: String
        event: ID
        party: ID
        media: [ID!]
        sharedWith: [ID!]
        contributors: [ID!]
        publish: Boolean
        category: MediaFolderCategory
        tags: [ID!]
        albumCover: [String!]
    }

    type Mutation {
        createMediaFolder(input: MediaFolderInput!): MediaFolderResult!
        updateMediaFolder(id: ID!, input: MediaFolderUpdateInput!): MediaFolderResult!
        deleteMediaFolder(id: ID!): MediaFolderResult!

        addMediaToMediaFolder(mediaFolderId: ID!, mediaId: ID!): MediaFolderResult!
        removeMediaFromMediaFolder(mediaFolderId: ID!, mediaId: ID!): MediaFolderResult!
    }
`; 