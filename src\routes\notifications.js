const express = require('express');
const router = express.Router();
const cors = require('cors');
const { verifyTokenLocally, verifyClerkUser } = require('../utils/clerk.util');
const { getTokenFromHeader } = require('../utils/serverSetup.util');
const { User } = require('../models/User');
const { readUserNotificationsFromQueue } = require('../utils/inappNotificationQueue.util');

router.use(cors({
    origin: '*',
    methods: ['GET'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
}));

const authenticate = async (req, res, next) => {
    try {
        const token = getTokenFromHeader(req);
        if (!token) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        const verifiedToken = await verifyTokenLocally(token);
        await verifyClerkUser(verifiedToken.sub);

        const dbUser = await User.findOne({ externalId: verifiedToken.sub });
        if (!dbUser) {
            return res.status(401).json({ error: 'User not found in database' });
        }

        req.user = {
            ...verifiedToken,
            _id: dbUser._id.toString()
        };
        next();
    } catch (error) {
        console.error('Authentication error in notifications:', error);
        return res.status(401).json({ error: 'Authentication failed' });
    }
};

const clientsByUserId = new Map();
const userIntervals = new Map();


router.get('/', authenticate, async (req, res) => {
    const userId = req.user._id;

    try {
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        });

        res.write(`data: ${JSON.stringify({ type: 'connection', message: 'Connected to notification stream' })}\n\n`);

        if (!clientsByUserId.has(userId)) {
            clientsByUserId.set(userId, new Set());
        }
        clientsByUserId.get(userId).add(res);
        console.log(`Client connected for userId: ${userId}. Total clients for user: ${clientsByUserId.get(userId).size}`);

        if (!userIntervals.has(userId)) {
            console.log(`Starting new interval for userId: ${userId}`);
            let userNotificationCounter = 0;

            const intervalId = setInterval(async () => {
                const currentUserClients = clientsByUserId.get(userId);

                if (!currentUserClients || currentUserClients.size === 0) {
                    console.log(`No clients for user ${userId}, interval ${intervalId} attempting to self-clear.`);
                    const currentIntervalData = userIntervals.get(userId);
                    if (currentIntervalData && currentIntervalData.intervalId === intervalId) {
                        clearInterval(intervalId);
                        userIntervals.delete(userId);
                        console.log(`Interval for user ${userId} cleared from within due to no clients.`);
                    }
                    return;
                }

                try {
                    const notifications = await readUserNotificationsFromQueue(userId);
                    let notificationsSentThisTick = false;

                    if (notifications.length > 0) {
                        notifications.forEach(notification => {
                            currentUserClients.forEach(clientRes => {
                                try {
                                    clientRes.write(`data: ${JSON.stringify(notification)}\n\n`);
                                } catch (e) {
                                    console.error(`Error writing notification to client for user ${userId}. Removing client. Error: ${e.message}`);
                                    clientRes.end();
                                    currentUserClients.delete(clientRes);
                                }
                            });
                        });
                        userNotificationCounter = 0;
                        notificationsSentThisTick = true;
                    }

                    if (!notificationsSentThisTick) {
                        userNotificationCounter++;
                    }

                    if (userNotificationCounter >= 10) {
                        currentUserClients.forEach(clientRes => {
                            try {
                                clientRes.write(`data: ${JSON.stringify({ type: "heartbeat", timestamp: Date.now(), userId })}\n\n`);
                            } catch (e) {
                                console.error(`Error writing heartbeat to client for user ${userId}. Removing client. Error: ${e.message}`);
                                clientRes.end();
                                currentUserClients.delete(clientRes);
                            }
                        });
                        userNotificationCounter = 0;
                    }

                } catch (error) {
                    console.error(`Error in notification interval for user ${userId}:`, error);
                }
            }, 1000);
            userIntervals.set(userId, { intervalId });
        }

        req.on('close', () => {
            console.log(`Client disconnecting for userId: ${userId}`);
            const userClientsSet = clientsByUserId.get(userId);
            if (userClientsSet) {
                userClientsSet.delete(res);
                console.log(`Client removed for userId: ${userId}. Remaining clients for user: ${userClientsSet.size}`);
                if (userClientsSet.size === 0) {
                    console.log(`Last client for userId: ${userId} disconnected. Clearing interval.`);
                    clientsByUserId.delete(userId);
                    const intervalData = userIntervals.get(userId);
                    if (intervalData) {
                        clearInterval(intervalData.intervalId);
                        userIntervals.delete(userId);
                        console.log(`Interval cleared for userId: ${userId}`);
                    }
                }
            }
            res.end();
        });

    } catch (error) {
        console.error('Error setting up SSE connection for notifications:', error);
        if (!res.headersSent) {
            res.status(500).json({ error: 'Failed to set up SSE connection for notifications' });
        } else {
            res.end();
        }
    }
});

const broadcastNotification = (notification, targetUserId) => {
    const userClients = clientsByUserId.get(targetUserId);
    if (userClients) {
        userClients.forEach(clientRes => {
            try {
                clientRes.write(`data: ${JSON.stringify(notification)}\n\n`);
            } catch (error) {
                console.error(`Error broadcasting notification to client for user ${targetUserId}:`, error.message);
                clientRes.end();
                userClients.delete(clientRes);
            }
        });
    }
};


module.exports = router; 