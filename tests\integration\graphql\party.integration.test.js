const { Graph<PERSON><PERSON>est<PERSON>el<PERSON>, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Party GraphQL Integration Tests', () => {
  let graphqlHelper;
  let testUser;
  let testEvent;
  let partyType;
  let serviceLocation;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    testEvent = await TestDataFactory.createEvent(testUser);
    partyType = await TestDataFactory.createPartyType();
    serviceLocation = await TestDataFactory.createServiceLocation();
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('Party Creation', () => {
    it('should create a new party successfully', async () => {
      const createPartyMutation = `
        mutation CreateParty($input: PartyInput!) {
          createParty(input: $input) {
            ... on PartyResponse {
              status
              message
              result {
                party {
                  id
                  name
                  expectedGuestCount
                  totalBudget
                  event {
                    id
                    name
                  }
                  partyType {
                    id
                    name
                  }
                  serviceLocation {
                    id
                    name
                  }
                }
              }
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          name: 'Birthday Celebration',
          partyType: partyType._id.toString(),
          time: new Date().toISOString(),
          expectedGuestCount: 25,
          totalBudget: 800,
          serviceLocation: serviceLocation._id.toString(),
          eventId: testEvent._id.toString()
        }
      };

      const response = await graphqlHelper.mutation(createPartyMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createParty.status).toBe('SUCCESS');
      expect(response.body.data.createParty.result.party.name).toBe('Birthday Celebration');
      expect(response.body.data.createParty.result.party.expectedGuestCount).toBe(25);
      expect(response.body.data.createParty.result.party.totalBudget).toBe(800);
    });

    it('should handle party creation with invalid event ID', async () => {
      const createPartyMutation = `
        mutation CreateParty($input: PartyInput!) {
          createParty(input: $input) {
            ... on PartyResponse {
              status
              message
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          name: 'Invalid Party',
          eventId: '507f1f77bcf86cd799439011', // Non-existent event ID
          expectedGuestCount: 10,
          totalBudget: 500
        }
      };

      const response = await graphqlHelper.mutation(createPartyMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createParty.status).toBe('FAILURE');
    });
  });

  describe('Party Queries', () => {
    let testParty;

    beforeEach(async () => {
      testParty = await TestDataFactory.createParty(testEvent);
    });

    it('should get party by ID', async () => {
      const getPartyQuery = `
        query GetPartyById($id: ID!) {
          getPartyById(id: $id) {
            ... on PartyResponse {
              status
              message
              result {
                party {
                  id
                  name
                  expectedGuestCount
                  actualGuestCount
                  totalBudget
                  totalExpenditure
                  event {
                    id
                    name
                  }
                  partyType {
                    id
                    name
                  }
                  serviceLocation {
                    id
                    name
                  }
                }
              }
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testParty._id.toString()
      };

      const response = await graphqlHelper.query(getPartyQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getPartyById.status).toBe('SUCCESS');
      expect(response.body.data.getPartyById.result.party.id).toBe(testParty._id.toString());
      expect(response.body.data.getPartyById.result.party.name).toBe(testParty.name);
    });

    it('should get parties with filters and pagination', async () => {
      // Create additional parties
      await TestDataFactory.createParty(testEvent, { name: 'Pool Party' });
      await TestDataFactory.createParty(testEvent, { name: 'Dinner Party' });

      const getPartiesQuery = `
        query GetParties($filter: PartyFilterInput, $pagination: PaginationInput) {
          getParties(filter: $filter, pagination: $pagination) {
            ... on PartiesResponse {
              status
              message
              result {
                parties {
                  id
                  name
                  expectedGuestCount
                  totalBudget
                }
              }
              pagination {
                totalItems
                totalPages
                currentPage
                pageSize
              }
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          event: testEvent._id.toString()
        },
        pagination: {
          limit: 10,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getPartiesQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getParties.status).toBe('SUCCESS');
      expect(response.body.data.getParties.result.parties).toHaveLength(3);
      expect(response.body.data.getParties.pagination.totalItems).toBe(3);
    });

    it('should handle non-existent party ID', async () => {
      const getPartyQuery = `
        query GetPartyById($id: ID!) {
          getPartyById(id: $id) {
            ... on PartyResponse {
              status
              message
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: '507f1f77bcf86cd799439011' // Non-existent party ID
      };

      const response = await graphqlHelper.query(getPartyQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getPartyById.status).toBe('FAILURE');
    });
  });

  describe('Party Updates', () => {
    let testParty;

    beforeEach(async () => {
      testParty = await TestDataFactory.createParty(testEvent);
    });

    it('should update party successfully', async () => {
      const updatePartyMutation = `
        mutation UpdateParty($id: ID!, $input: PartyUpdateInput!) {
          updateParty(id: $id, input: $input) {
            ... on PartyResponse {
              status
              message
              result {
                party {
                  id
                  name
                  expectedGuestCount
                  totalBudget
                }
              }
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testParty._id.toString(),
        input: {
          name: 'Updated Party Name',
          expectedGuestCount: 50,
          totalBudget: 1200
        }
      };

      const response = await graphqlHelper.mutation(updatePartyMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateParty.status).toBe('SUCCESS');
      expect(response.body.data.updateParty.result.party.name).toBe('Updated Party Name');
      expect(response.body.data.updateParty.result.party.expectedGuestCount).toBe(50);
      expect(response.body.data.updateParty.result.party.totalBudget).toBe(1200);
    });
  });

  describe('Party Deletion', () => {
    let testParty;

    beforeEach(async () => {
      testParty = await TestDataFactory.createParty(testEvent);
    });

    it('should delete party successfully', async () => {
      const deletePartyMutation = `
        mutation DeleteParty($id: ID!) {
          deleteParty(id: $id) {
            ... on PartyResponse {
              status
              message
            }
            ... on PartyErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testParty._id.toString()
      };

      const response = await graphqlHelper.mutation(deletePartyMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.deleteParty.status).toBe('SUCCESS');

      // Verify party is deleted
      const getPartyQuery = `
        query GetPartyById($id: ID!) {
          getPartyById(id: $id) {
            ... on PartyResponse {
              status
            }
            ... on PartyErrorResponse {
              status
              message
            }
          }
        }
      `;

      const getResponse = await graphqlHelper.query(getPartyQuery, variables);
      expect(getResponse.body.data.getPartyById.status).toBe('FAILURE');
    });
  });
});
