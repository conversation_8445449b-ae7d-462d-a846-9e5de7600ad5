const { model, Schema } = require('mongoose');

const RECEIVER_TYPES = ['Host', 'Cohost', 'Collaborators', 'Guest', 'Organizer', 'Member'];

function extractVariables(text) {
    if (!text) return [];
    const matches = text.match(/{{([^}]+)}}/g) || [];
    return matches.map(match => match.slice(2, -2));
}

const queryParamSchema = new Schema({
    key: { type: String, required: true },
    value: { 
        type: String, 
        required: true,
        default: ''
    }
}, { _id: false });

const navigationSchema = new Schema({
    route: { type: String, required: true },
    params: [{ type: String }],
    queryParams: [{ type: String }]
}, { _id: false });

const templateSchema = new Schema({
    subject: { type: String, required: true },
    message: { type: String, required: true },
    navigation: { type: navigationSchema }
}, { _id: false });

const channelsSchema = new Schema({
    activity: { type: Boolean, default: false },
    sms: { type: Boolean, default: false },
    whatsapp: { type: Boolean, default: false },
    inApp: { type: Boolean, default: false },
    push: { type: Boolean, default: false },
    email: { type: Boolean, default: false }
}, { _id: false });

const notificationEventSchema = new Schema({
    receivers: [{
        type: String,
        required: true,
        enum: RECEIVER_TYPES,
        validate: {
            validator: function(receivers) {
                return receivers.length > 0;
            },
            message: 'At least one receiver is required'
        }
    }],
    channels: { type: channelsSchema, required: true },
    templates: { type: templateSchema, required: true },
    requiredData: [{ type: String }] 
}, { _id: false });

notificationEventSchema.pre('save', function(next) {
    const subjectVars = extractVariables(this.templates.subject);
    const messageVars = extractVariables(this.templates.message);
    
    const queryParamVars = this.templates.navigation?.queryParams?.reduce((vars, param) => {
        return [...vars, ...extractVariables(param)];
    }, []) || [];
    
    this.requiredData = [...new Set([...subjectVars, ...messageVars, ...queryParamVars])];
    next();
});

const notificationConfigSchema = new Schema({
    eventCategory: { type: String, required: true },
    eventType: { type: String, required: true },
    config: { type: notificationEventSchema, required: true }
}, { timestamps: true });

const NotificationConfig = model('md_notification_configs', notificationConfigSchema);

module.exports = NotificationConfig;