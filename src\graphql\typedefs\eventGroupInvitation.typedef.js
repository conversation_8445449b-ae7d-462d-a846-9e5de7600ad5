const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const eventGroupInvitationTypeDefs = gql`
    ${sharedTypeDef}

    enum EventGroupInvitationStatus {
        PENDING
        ACCEPTED
        REJECTED
    }

    type EventGroupInvitation {
        id: ID!
        user: User!
        eventGroup: EventGroup!
        status: EventGroupInvitationStatus!
        createdAt: DateTime!
        updatedAt: DateTime!
    }

    input EventGroupInvitationFilterInput {
        user: UserFilterInput
        eventGroupId: ID
        eventGroup: EventGroupFilterInput
        status: EventGroupInvitationStatus
        createdAt: DateTimeRangeInput
        updatedAt: DateTimeRangeInput
    }

    type EventGroupInvitationWrapper {
        eventGroupInvitation: EventGroupInvitation!
    }

    type EventGroupInvitationsWrapper {
        eventGroupInvitations: [EventGroupInvitation!]!
    }

    type EventGroupInvitationResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventGroupInvitationWrapper!
    }

    type EventGroupInvitationsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventGroupInvitationsWrapper!
        pagination: PaginationInfo!
    }

    type EventGroupInvitationErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union EventGroupInvitationResult = EventGroupInvitationResponse | EventGroupInvitationErrorResponse
    union EventGroupInvitationsResult = EventGroupInvitationsResponse | EventGroupInvitationErrorResponse

    input CreateEventGroupInvitationInput {
        userId: ID!
        eventGroupId: ID!
        status: EventGroupInvitationStatus
    }

    input MemberInput {
        firstName: String!
        lastName: String
        phone: String!
        email: String
    }

    input SendEventGroupInvitationInput {
        members: [MemberInput!]!
        eventGroupId: ID!
    }

    input UpdateEventGroupInvitationInput {
        status: EventGroupInvitationStatus!
    }

    extend type Query {
        getEventGroupInvitationById(id: ID!): EventGroupInvitationResult!
        getEventGroupInvitations(filter: EventGroupInvitationFilterInput, pagination: PaginationInput): EventGroupInvitationsResult!
    }

    extend type Mutation {
        createEventGroupInvitation(input: CreateEventGroupInvitationInput!): EventGroupInvitationResult!
        sendEventGroupInvitation(input: SendEventGroupInvitationInput!): EventGroupInvitationResult!
        updateEventGroupInvitation(id: ID!, input: UpdateEventGroupInvitationInput!): EventGroupInvitationResult!
        deleteEventGroupInvitation(id: ID!): EventGroupInvitationResult!
    }
`;

module.exports = eventGroupInvitationTypeDefs;
