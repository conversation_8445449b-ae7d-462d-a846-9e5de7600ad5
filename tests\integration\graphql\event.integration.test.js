const { Graph<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Event GraphQL Integration Tests', () => {
  let graphqlHelper;
  let testUser;
  let eventType;
  let serviceLocation;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    eventType = await TestDataFactory.createEventType();
    serviceLocation = await TestDataFactory.createServiceLocation();
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('Event Creation', () => {
    it('should create a new event successfully', async () => {
      const createEventMutation = `
        mutation CreateEvent($input: EventInput!) {
          createEvent(input: $input) {
            ... on EventResponse {
              status
              message
              result {
                event {
                  id
                  name
                  description
                  startDate
                  endDate
                  budget
                  eventStatus
                  mainHost {
                    id
                    user {
                      firstName
                    }
                  }
                }
              }
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          eventType: eventType._id.toString(),
          name: 'Test Birthday Party',
          description: 'A wonderful birthday celebration',
          startDate: new Date().toISOString(),
          endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          budget: 1500,
          location: serviceLocation._id.toString(),
          eventStatus: 'PLANNED'
        }
      };

      const response = await graphqlHelper.mutation(createEventMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.createEvent.status).toBe('SUCCESS');
      expect(response.body.data.createEvent.result.event.name).toBe('Test Birthday Party');
      expect(response.body.data.createEvent.result.event.budget).toBe(1500);
      expect(response.body.data.createEvent.result.event.eventStatus).toBe('PLANNED');
    });

    it('should handle event creation with invalid data', async () => {
      const createEventMutation = `
        mutation CreateEvent($input: EventInput!) {
          createEvent(input: $input) {
            ... on EventResponse {
              status
              message
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          // Missing required fields like name, eventType
          description: 'Invalid event',
          budget: -100 // Invalid negative budget
        }
      };

      const response = await graphqlHelper.mutation(createEventMutation, variables);

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Event Queries', () => {
    let testEvent;

    beforeEach(async () => {
      testEvent = await TestDataFactory.createEvent(testUser);
    });

    it('should get event by ID', async () => {
      const getEventQuery = `
        query GetEventById($id: ID!) {
          getEventById(id: $id) {
            ... on EventResponse {
              status
              message
              result {
                event {
                  id
                  name
                  description
                  budget
                  eventStatus
                  mainHost {
                    id
                    user {
                      firstName
                      email
                    }
                  }
                  eventType {
                    id
                    name
                  }
                  location {
                    id
                    name
                  }
                }
              }
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testEvent._id.toString()
      };

      const response = await graphqlHelper.query(getEventQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getEventById.status).toBe('SUCCESS');
      expect(response.body.data.getEventById.result.event.id).toBe(testEvent._id.toString());
      expect(response.body.data.getEventById.result.event.name).toBe(testEvent.name);
    });

    it('should get events with filters and pagination', async () => {
      // Create additional events
      await TestDataFactory.createEvent(testUser, { name: 'Wedding Event' });
      await TestDataFactory.createEvent(testUser, { name: 'Corporate Event' });

      const getEventsQuery = `
        query GetEvents($filter: EventFilterInput, $pagination: PaginationInput) {
          getEvents(filter: $filter, pagination: $pagination) {
            ... on EventsResponse {
              status
              message
              result {
                events {
                  id
                  name
                  budget
                  eventStatus
                }
              }
              pagination {
                totalItems
                totalPages
                currentPage
                pageSize
              }
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filter: {
          eventStatus: 'PLANNED'
        },
        pagination: {
          limit: 10,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getEventsQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getEvents.status).toBe('SUCCESS');
      expect(response.body.data.getEvents.result.events).toHaveLength(3);
      expect(response.body.data.getEvents.pagination.totalItems).toBe(3);
    });

    it('should get user events', async () => {
      const getUserEventsQuery = `
        query GetUserEvents($filter: EventFilterInput, $pagination: PaginationInput) {
          getUserEvents(filter: $filter, pagination: $pagination) {
            ... on EventsResponse {
              status
              message
              result {
                events {
                  id
                  name
                  mainHost {
                    user {
                      id
                    }
                  }
                }
              }
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getUserEventsQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUserEvents.status).toBe('SUCCESS');
      expect(response.body.data.getUserEvents.result.events.length).toBeGreaterThan(0);
    });
  });

  describe('Event Updates', () => {
    let testEvent;

    beforeEach(async () => {
      testEvent = await TestDataFactory.createEvent(testUser);
    });

    it('should update event successfully', async () => {
      const updateEventMutation = `
        mutation UpdateEvent($id: ID!, $input: EventUpdateInput!) {
          updateEvent(id: $id, input: $input) {
            ... on EventResponse {
              status
              message
              result {
                event {
                  id
                  name
                  description
                  budget
                  eventStatus
                }
              }
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testEvent._id.toString(),
        input: {
          name: 'Updated Event Name',
          description: 'Updated description',
          budget: 2000,
          eventStatus: 'CONFIRMED'
        }
      };

      const response = await graphqlHelper.mutation(updateEventMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateEvent.status).toBe('SUCCESS');
      expect(response.body.data.updateEvent.result.event.name).toBe('Updated Event Name');
      expect(response.body.data.updateEvent.result.event.budget).toBe(2000);
      expect(response.body.data.updateEvent.result.event.eventStatus).toBe('CONFIRMED');
    });
  });

  describe('Event Deletion', () => {
    let testEvent;

    beforeEach(async () => {
      testEvent = await TestDataFactory.createEvent(testUser);
    });

    it('should delete event successfully', async () => {
      const deleteEventMutation = `
        mutation DeleteEvent($id: ID!) {
          deleteEvent(id: $id) {
            ... on EventResponse {
              status
              message
            }
            ... on EventErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        id: testEvent._id.toString()
      };

      const response = await graphqlHelper.mutation(deleteEventMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.deleteEvent.status).toBe('SUCCESS');

      // Verify event is deleted
      const getEventQuery = `
        query GetEventById($id: ID!) {
          getEventById(id: $id) {
            ... on EventResponse {
              status
            }
            ... on EventErrorResponse {
              status
              message
            }
          }
        }
      `;

      const getResponse = await graphqlHelper.query(getEventQuery, variables);
      expect(getResponse.body.data.getEventById.status).toBe('FAILURE');
    });
  });
});
