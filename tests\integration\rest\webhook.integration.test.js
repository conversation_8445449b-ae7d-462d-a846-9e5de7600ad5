const request = require('supertest');
const { TestDataFactory } = require('../utils/testHelpers');

describe('Webhook REST API Integration Tests', () => {
  let app;

  beforeAll(() => {
    app = global.testApp;
  });

  describe('POST /webhook/user-added', () => {
    it('should handle user-added webhook successfully', async () => {
      const webhookPayload = {
        data: {
          id: 'user_test123',
          first_name: '<PERSON>',
          last_name: '<PERSON><PERSON>',
          email_addresses: [
            {
              email_address: '<EMAIL>',
              verification: {
                status: 'verified'
              }
            }
          ],
          phone_numbers: [
            {
              phone_number: '+1234567890',
              verification: {
                status: 'verified'
              }
            }
          ],
          created_at: Date.now(),
          updated_at: Date.now()
        },
        type: 'user.created'
      };

      // Mock the webhook signature verification
      const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
      jest.spyOn(verifyWebhookSignature, 'default').mockReturnValue(webhookPayload);

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .set('svix-id', 'msg_test123')
        .set('svix-timestamp', Date.now().toString())
        .set('svix-signature', 'v1,test-signature')
        .send(webhookPayload);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Webhook received successfully');
    });

    it('should handle invalid webhook signature', async () => {
      const webhookPayload = {
        data: {
          id: 'user_test123'
        },
        type: 'user.created'
      };

      // Mock the webhook signature verification to throw an error
      const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
      jest.spyOn(verifyWebhookSignature, 'default').mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .send(webhookPayload);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid webhook');
    });

    it('should handle malformed webhook payload', async () => {
      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .send('invalid json');

      expect(response.status).toBe(400);
    });
  });

  describe('Webhook Security', () => {
    it('should reject webhook without required headers', async () => {
      const webhookPayload = {
        data: {
          id: 'user_test123'
        },
        type: 'user.created'
      };

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .send(webhookPayload);

      expect(response.status).toBe(400);
    });

    it('should handle webhook with expired timestamp', async () => {
      const webhookPayload = {
        data: {
          id: 'user_test123'
        },
        type: 'user.created'
      };

      // Mock expired timestamp
      const expiredTimestamp = Date.now() - (6 * 60 * 1000); // 6 minutes ago

      const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
      jest.spyOn(verifyWebhookSignature, 'default').mockImplementation(() => {
        throw new Error('Timestamp too old');
      });

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .set('svix-id', 'msg_test123')
        .set('svix-timestamp', expiredTimestamp.toString())
        .set('svix-signature', 'v1,test-signature')
        .send(webhookPayload);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid webhook');
    });
  });

  describe('User Creation from Webhook', () => {
    it('should create user in database from webhook payload', async () => {
      const webhookPayload = {
        data: {
          id: 'user_webhook_test',
          first_name: 'Jane',
          last_name: 'Smith',
          email_addresses: [
            {
              email_address: '<EMAIL>',
              verification: {
                status: 'verified'
              }
            }
          ],
          phone_numbers: [
            {
              phone_number: '+1987654321',
              verification: {
                status: 'verified'
              }
            }
          ],
          created_at: Date.now(),
          updated_at: Date.now()
        },
        type: 'user.created'
      };

      // Mock successful webhook verification
      const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
      jest.spyOn(verifyWebhookSignature, 'default').mockReturnValue(webhookPayload);

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .set('svix-id', 'msg_test456')
        .set('svix-timestamp', Date.now().toString())
        .set('svix-signature', 'v1,test-signature')
        .send(webhookPayload);

      expect(response.status).toBe(200);

      // Verify user was created in database
      const { User } = require('../../../src/models/User');
      const createdUser = await User.findOne({ externalId: 'user_webhook_test' });
      
      expect(createdUser).toBeTruthy();
      expect(createdUser.firstName).toBe('Jane');
      expect(createdUser.lastName).toBe('Smith');
      expect(createdUser.email).toBe('<EMAIL>');
      expect(createdUser.phone).toBe('+1987654321');
    });

    it('should handle duplicate user creation gracefully', async () => {
      // First, create a user manually
      await TestDataFactory.createUser({
        externalId: 'user_duplicate_test',
        firstName: 'Existing',
        lastName: 'User',
        email: '<EMAIL>'
      });

      const webhookPayload = {
        data: {
          id: 'user_duplicate_test', // Same external ID
          first_name: 'Updated',
          last_name: 'User',
          email_addresses: [
            {
              email_address: '<EMAIL>',
              verification: {
                status: 'verified'
              }
            }
          ],
          phone_numbers: [
            {
              phone_number: '+1111111111',
              verification: {
                status: 'verified'
              }
            }
          ],
          created_at: Date.now(),
          updated_at: Date.now()
        },
        type: 'user.created'
      };

      const verifyWebhookSignature = require('../../../src/utils/verifyWebhookSignature.util');
      jest.spyOn(verifyWebhookSignature, 'default').mockReturnValue(webhookPayload);

      const response = await request(app)
        .post('/webhook/user-added')
        .set('Content-Type', 'application/json')
        .set('svix-id', 'msg_duplicate')
        .set('svix-timestamp', Date.now().toString())
        .set('svix-signature', 'v1,test-signature')
        .send(webhookPayload);

      // Should still return success even if user already exists
      expect(response.status).toBe(200);
    });
  });
});
