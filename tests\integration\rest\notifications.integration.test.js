const request = require('supertest');
const { TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('Notifications SSE REST API Integration Tests', () => {
  let app;
  let testUser;

  beforeAll(() => {
    app = global.testApp;
  });

  beforeEach(async () => {
    testUser = await TestDataFactory.createUser();
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('GET /inapp_notifications_stream', () => {
    it('should establish SSE connection successfully with valid authentication', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .timeout(5000);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toBe('text/event-stream');
      expect(response.headers['cache-control']).toBe('no-cache');
      expect(response.headers['connection']).toBe('keep-alive');
    });

    it('should reject connection without authentication', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });

    it('should handle invalid authentication token', async () => {
      // Mock invalid token verification
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockRejectedValue(new Error('Invalid token'));

      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer invalid-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication failed');
    });

    it('should handle user not found in database', async () => {
      // Mock token verification but user not in DB
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyTokenLocally.mockResolvedValue({
        sub: 'non-existent-user-id',
        metadata: { role: ['user'] }
      });

      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('User not found in database');
    });
  });

  describe('SSE Connection Behavior', () => {
    it('should send initial connection message', (done) => {
      const req = request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .buffer(false)
        .parse((res, callback) => {
          let data = '';
          res.on('data', (chunk) => {
            data += chunk.toString();
            
            // Check for initial connection message
            if (data.includes('Connected to notification stream')) {
              const lines = data.split('\n');
              const dataLine = lines.find(line => line.startsWith('data: '));
              
              if (dataLine) {
                const messageData = JSON.parse(dataLine.substring(6));
                expect(messageData.type).toBe('connection');
                expect(messageData.message).toBe('Connected to notification stream');
                callback(null, data);
                done();
              }
            }
          });
          
          res.on('error', (err) => {
            callback(err);
            done(err);
          });
        });

      req.end();
    });

    it('should handle connection timeout gracefully', (done) => {
      const req = request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .timeout(2000)
        .buffer(false);

      req.on('timeout', () => {
        // This is expected behavior for SSE connections
        done();
      });

      req.on('error', (err) => {
        if (err.code === 'ECONNABORTED' && err.timeout) {
          // Expected timeout
          done();
        } else {
          done(err);
        }
      });

      req.end();
    });
  });

  describe('CORS Configuration', () => {
    it('should include proper CORS headers in SSE response', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .set('Origin', 'https://example.com')
        .timeout(1000);

      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toBe('GET');
      expect(response.headers['access-control-allow-headers']).toContain('Authorization');
    });

    it('should handle preflight OPTIONS request', async () => {
      const response = await request(app)
        .options('/inapp_notifications_stream')
        .set('Origin', 'https://example.com')
        .set('Access-Control-Request-Method', 'GET')
        .set('Access-Control-Request-Headers', 'Authorization');

      expect(response.status).toBe(204);
      expect(response.headers['access-control-allow-origin']).toBe('*');
      expect(response.headers['access-control-allow-methods']).toBe('GET');
    });
  });

  describe('Authentication Edge Cases', () => {
    it('should handle malformed authorization header', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'InvalidFormat token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });

    it('should handle missing authorization header', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });

    it('should handle empty authorization header', async () => {
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', '')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication required');
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      const { User } = require('../../../src/models/User');
      jest.spyOn(User, 'findOne').mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication failed');
    });

    it('should handle Clerk service unavailable', async () => {
      // Mock Clerk service error
      const clerkUtils = require('../../../src/utils/clerk.util');
      clerkUtils.verifyClerkUser.mockRejectedValue(new Error('Clerk service unavailable'));

      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Authentication failed');
    });
  });

  describe('User-Specific Notifications', () => {
    it('should isolate notifications by user ID', async () => {
      // Create another user
      const otherUser = await TestDataFactory.createUser({
        externalId: 'other-user-id',
        email: '<EMAIL>'
      });

      // Mock notification queue utility
      const notificationQueue = require('../../../src/utils/inappNotificationQueue.util');
      jest.spyOn(notificationQueue, 'readUserNotificationsFromQueue')
        .mockImplementation((userId) => {
          if (userId === testUser._id.toString()) {
            return Promise.resolve([
              {
                id: 'notif1',
                type: 'task_assigned',
                message: 'You have been assigned a new task',
                userId: testUser._id.toString()
              }
            ]);
          }
          return Promise.resolve([]);
        });

      // Test that user only receives their own notifications
      const response = await request(app)
        .get('/inapp_notifications_stream')
        .set('Authorization', 'Bearer test-token')
        .set('Accept', 'text/event-stream')
        .timeout(3000);

      expect(response.status).toBe(200);
      // Additional verification would require parsing SSE stream
    });
  });

  describe('Connection Management', () => {
    it('should handle multiple concurrent connections for same user', async () => {
      const promises = [];
      
      // Create multiple concurrent connections
      for (let i = 0; i < 3; i++) {
        const promise = request(app)
          .get('/inapp_notifications_stream')
          .set('Authorization', 'Bearer test-token')
          .set('Accept', 'text/event-stream')
          .timeout(2000);
        
        promises.push(promise);
      }

      // All connections should be successful
      const responses = await Promise.allSettled(promises);
      
      responses.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          expect(result.value.status).toBe(200);
        } else {
          // Timeout is acceptable for SSE connections
          expect(result.reason.code).toBe('ECONNABORTED');
        }
      });
    });
  });
});
