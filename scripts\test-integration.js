#!/usr/bin/env node

/**
 * Integration Test Runner
 * 
 * This script sets up the environment and runs integration tests
 * with proper configuration and cleanup.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if .env.test exists
  const envTestPath = path.join(process.cwd(), '.env.test');
  if (!fs.existsSync(envTestPath)) {
    log('❌ .env.test file not found. Creating default...', 'yellow');
    // Could create a default .env.test here if needed
  } else {
    log('✅ .env.test file found', 'green');
  }
  
  // Check if jest.config.js exists
  const jestConfigPath = path.join(process.cwd(), 'jest.config.js');
  if (!fs.existsSync(jestConfigPath)) {
    log('❌ jest.config.js not found', 'red');
    process.exit(1);
  } else {
    log('✅ jest.config.js found', 'green');
  }
  
  // Check if integration test directory exists
  const integrationTestDir = path.join(process.cwd(), 'tests', 'integration');
  if (!fs.existsSync(integrationTestDir)) {
    log('❌ Integration test directory not found', 'red');
    process.exit(1);
  } else {
    log('✅ Integration test directory found', 'green');
  }
}

function runTests(options = {}) {
  return new Promise((resolve, reject) => {
    log('🚀 Starting integration tests...', 'cyan');
    
    const jestArgs = [
      '--config', 'jest.config.js',
      '--selectProjects', 'integration',
      '--runInBand', // Run tests serially for integration tests
      '--forceExit', // Ensure Jest exits after tests complete
    ];
    
    // Add additional options
    if (options.watch) {
      jestArgs.push('--watch');
    }
    
    if (options.coverage) {
      jestArgs.push('--coverage');
    }
    
    if (options.verbose) {
      jestArgs.push('--verbose');
    }
    
    if (options.pattern) {
      jestArgs.push(options.pattern);
    }
    
    // Set environment variables
    const env = {
      ...process.env,
      NODE_ENV: 'test',
      FORCE_COLOR: '1' // Ensure colored output
    };
    
    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      env,
      cwd: process.cwd()
    });
    
    jest.on('close', (code) => {
      if (code === 0) {
        log('✅ All integration tests passed!', 'green');
        resolve();
      } else {
        log(`❌ Integration tests failed with exit code ${code}`, 'red');
        reject(new Error(`Tests failed with exit code ${code}`));
      }
    });
    
    jest.on('error', (error) => {
      log(`❌ Failed to start Jest: ${error.message}`, 'red');
      reject(error);
    });
  });
}

function printUsage() {
  log('Integration Test Runner', 'bright');
  log('');
  log('Usage: node scripts/test-integration.js [options]', 'cyan');
  log('');
  log('Options:', 'yellow');
  log('  --watch     Run tests in watch mode');
  log('  --coverage  Generate coverage report');
  log('  --verbose   Show verbose output');
  log('  --pattern   Run tests matching pattern (e.g., user.integration.test.js)');
  log('  --help      Show this help message');
  log('');
  log('Examples:', 'yellow');
  log('  node scripts/test-integration.js');
  log('  node scripts/test-integration.js --watch');
  log('  node scripts/test-integration.js --coverage');
  log('  node scripts/test-integration.js --pattern user.integration.test.js');
}

async function main() {
  const args = process.argv.slice(2);
  
  // Parse command line arguments
  const options = {
    watch: args.includes('--watch'),
    coverage: args.includes('--coverage'),
    verbose: args.includes('--verbose'),
    help: args.includes('--help')
  };
  
  // Find pattern argument
  const patternIndex = args.indexOf('--pattern');
  if (patternIndex !== -1 && patternIndex + 1 < args.length) {
    options.pattern = args[patternIndex + 1];
  }
  
  if (options.help) {
    printUsage();
    return;
  }
  
  try {
    log('🧪 GraphQL API Integration Test Runner', 'bright');
    log('=====================================', 'bright');
    log('');
    
    checkPrerequisites();
    log('');
    
    await runTests(options);
    
  } catch (error) {
    log(`❌ Test execution failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\n🛑 Test execution interrupted', 'yellow');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\n🛑 Test execution terminated', 'yellow');
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    log(`❌ Unexpected error: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runTests, checkPrerequisites };
