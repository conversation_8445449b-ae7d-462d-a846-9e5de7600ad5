const { isValidObjectId } = require('../../../utils/validation.util');
const buildMdEventTypeQuery = require('../filters/mdEventType.filter');
const buildAddressQuery = require('../filters/address.filter');
const buildPartyQuery = require('../filters/party.filter');
const MdEventType = require('../../../models/MdEventType');
const Party = require('../../../models/Party');
const MdServiceLocation = require('../../../models/MdServiceLocation');
const buildServiceLocationQuery = require('../filters/mdServiceLocation.filter');
const Guest = require('../../../models/Guest');
const Host = require('../../../models/Host');
const Event = require('../../../models/Event');
const EventGroup = require('../../../models/EventGroup');

const buildEventQuery = async (filters, userId) => {
    let query = {};
    let postQueryFilters = {};
    let paginationModifier = null;

    const userEventGroups = await EventGroup.find({
        $or: [
            { members: userId },
            { organizers: userId }
        ]
    });

    const eventGroupIds = userEventGroups.map(group => group._id);

    if (filters?.eventGroupId) {
        const eventGroup = await EventGroup.findById(filters.eventGroupId);
        if (!eventGroup) {
            throw new Error('Event group not found');
        }

        const isMember = eventGroup.members.some(member => member.toString() === userId.toString());
        const isOrganizer = eventGroup.organizers.some(organizer => organizer.toString() === userId.toString());

        if (!isMember && !isOrganizer) {
            throw new Error('You do not have access to this event group');
        }

        query.eventGroupId = filters.eventGroupId;
    }

    const [userHost, userGuests] = await Promise.all([
        Host.findOne({ userId }),
        Guest.find({ user: userId })
    ]);

    const partyIds = userGuests.map(g => g.party);
    const [userParties, partyCoHosts] = await Promise.all([
        Party.find({ _id: { $in: partyIds } }).select('eventId'),
        userHost ? Party.find({ coHosts: userHost._id }).select('eventId') : []
    ]);

    const eventIds = new Set();
    
    if (userHost) {
        const hostEvents = await Event.find({
            $or: [
                { mainHost: userHost._id },
                { coHosts: userHost._id }
            ]
        }).select('_id');
        hostEvents.forEach(e => eventIds.add(e._id.toString()));
    }

    userParties.forEach(p => eventIds.add(p.eventId.toString()));

    partyCoHosts.forEach(p => eventIds.add(p.eventId.toString()));

    const queryConditions = [];

    if (eventIds.size > 0) {
        queryConditions.push({ _id: { $in: Array.from(eventIds) } });
    }

    if (eventGroupIds.length > 0) {
        queryConditions.push({ eventGroupId: { $in: eventGroupIds } });
    }

    if (queryConditions.length > 0) {
        query.$or = queryConditions;
    }

    if (filters) {
        if (filters.name) {
            query.name = { $regex: filters.name, $options: 'i' };
        }

        if (filters.description) {
            query.description = { $regex: filters.description, $options: 'i' };
        }

        if (filters.eventType) {
            if (typeof filters.eventType === 'string') {
                if (isValidObjectId(filters.eventType)) {
                    query.eventType = filters.eventType;
                } else {
                    throw new Error('Invalid eventType ID provided');
                }
            } else if (typeof filters.eventType === 'object') {
                const eventTypeQuery = await buildMdEventTypeQuery(filters.eventType);
                if (Object.keys(eventTypeQuery).length > 0) {
                    const matchingEventTypes = await MdEventType.find(eventTypeQuery).select('_id');
                    if (matchingEventTypes.length > 0) {
                        query.eventType = { $in: matchingEventTypes.map(et => et._id) };
                    } else {
                        query.eventType = { $in: [] };
                    }
                }
            }
        }

        if (filters.location) {
            if (typeof filters.location === 'string') {
                if (isValidObjectId(filters.location)) {
                    query.location = filters.location;
                } else {
                    throw new Error('Invalid location ID provided');
                }
            } else if (typeof filters.location === 'object') {
                const locationQuery = await buildServiceLocationQuery(filters.location);
                if (Object.keys(locationQuery).length > 0) {
                    const matchingLocations = await MdServiceLocation.find(locationQuery).select('_id');
                    if (matchingLocations.length > 0) {
                        query.location = { $in: matchingLocations.map(loc => loc._id) };
                    } else {
                        query.location = { $in: [] };
                    }
                }
            }
        }

        if (filters.budget) {
            query.budget = {};
            if (filters.budget.min !== undefined) {
                query.budget.$gte = filters.budget.min;
            }
            if (filters.budget.max !== undefined) {
                query.budget.$lte = filters.budget.max;
            }
        }

        if (filters.parties) {
            const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filters.parties);
            
            if (Object.keys(partyQuery).length > 0) {
                const matchingParties = await Party.find(partyQuery);
                const filteredParties = await applyPostQueryFilters(matchingParties);
                
                if (filteredParties.length > 0) {
                    const partyEventIds = [...new Set(filteredParties.map(party => party.eventId))];
                    query._id = { $in: partyEventIds.filter(id => eventIds.has(id.toString())) };
                } else {
                    query._id = { $in: [] };
                }
            }
        }

        if (filters.dateRange) {
            const partyQuery = {};
            
            if (filters.dateRange.start) {
                partyQuery.time = partyQuery.time || {};
                partyQuery.time.$gte = filters.dateRange.start;
            }
            
            if (filters.dateRange.end) {
                partyQuery.time = partyQuery.time || {};
                partyQuery.time.$lte = filters.dateRange.end;
            }

            if (Object.keys(partyQuery).length > 0) {
                const matchingParties = await Party.find({
                    ...partyQuery,
                    eventId: { $in: Array.from(eventIds) }
                });
                if (matchingParties.length > 0) {
                    const dateEventIds = [...new Set(matchingParties.map(party => party.eventId))];
                    query._id = { $in: dateEventIds.filter(id => eventIds.has(id.toString())) };
                } else {
                    query._id = { $in: [] };
                }
            }
        }

        if (filters.presentAndUpcoming !== undefined) {
            const currentDate = new Date();
            const utcDate = new Date(Date.UTC(
                currentDate.getUTCFullYear(),
                currentDate.getUTCMonth(),
                currentDate.getUTCDate(),
                0, 0, 0, 0
            ));

            postQueryFilters.customSort = {
                presentAndUpcoming: filters.presentAndUpcoming,
                currentDate: utcDate
            };

            paginationModifier = async (baseCount, findQuery) => {
                const allEvents = await Event.find(findQuery);
                const eventsWithDates = await Promise.all(allEvents.map(async (event) => {
                    const parties = await Party.find({ eventId: event._id })
                        .select('time')
                        .sort({ time: -1 })
                        .limit(1)
                        .lean();

                    const actualEndDate = parties[0]?.time || event.endDate || null;
                    return { event, actualEndDate };
                }));

                const filteredEvents = eventsWithDates.filter(({ actualEndDate }) => {
                    if (!actualEndDate) {
                        return filters.presentAndUpcoming;
                    }

                    const eventDate = new Date(Date.UTC(
                        actualEndDate.getUTCFullYear(),
                        actualEndDate.getUTCMonth(),
                        actualEndDate.getUTCDate(),
                        0, 0, 0, 0
                    ));

                    if (filters.presentAndUpcoming) {
                        return eventDate >= utcDate;
                    } else {
                        return eventDate < utcDate;
                    }
                });

                return filteredEvents.length;
            };
        }

        if (filters.userType) {
            switch (filters.userType) {
                case 'HOST':
                    if (!userHost) {
                        query._id = { $in: [] };
                    } else {
                        const hostEventIds = new Set();
                        const hostEvents = await Event.find({
                            $or: [
                                { mainHost: userHost._id },
                                { coHosts: userHost._id }
                            ]
                        }).select('_id');
                        hostEvents.forEach(e => hostEventIds.add(e._id.toString()));

                        partyCoHosts.forEach(p => hostEventIds.add(p.eventId.toString()));

                        query._id = { $in: Array.from(hostEventIds) };
                    }
                    break;
        
                case 'GUEST':
                    if (userGuests.length === 0) {
                        query._id = { $in: [] };
                    } else {
                        const guestEventIds = new Set(userParties.map(p => p.eventId.toString()));
                        query._id = { $in: Array.from(guestEventIds) };
                    }
                    break;
        
                default:
                    throw new Error('Invalid userType provided');
            }
        }

        if (filters.eventStatus) {
            query.eventStatus = filters.eventStatus;
        }
    }

    return {
        query,
        postQueryFilters,
        paginationModifier,
        applyPostQueryFilters: async (events) => {
            if (Object.keys(postQueryFilters).length === 0) {
                return events;
            }

            let sortedEvents = [...events];

            if (postQueryFilters.customSort) {
                sortedEvents = await Promise.all(sortedEvents.map(async (event) => {
                    const parties = await Party.find({ eventId: event._id })
                        .select('time')
                        .sort({ time: -1 })
                        .limit(1)
                        .lean();

                    const actualEndDate = parties[0]?.time || event.endDate || null;
                    return {
                        ...(event.toObject ? event.toObject() : event),
                        actualEndDate
                    };
                }));

                sortedEvents = sortedEvents.filter(event => {
                    const eventEndDate = event.actualEndDate ? new Date(event.actualEndDate) : null;
                    
                    if (!eventEndDate) {
                        return postQueryFilters.customSort.presentAndUpcoming;
                    }

                    const eventDate = new Date(Date.UTC(
                        eventEndDate.getUTCFullYear(),
                        eventEndDate.getUTCMonth(),
                        eventEndDate.getUTCDate(),
                        0, 0, 0, 0
                    ));

                    const currentDate = postQueryFilters.customSort.currentDate;

                    if (postQueryFilters.customSort.presentAndUpcoming) {
                        return eventDate >= currentDate;
                    } else {
                        return eventDate < currentDate;
                    }
                });

                sortedEvents.sort((a, b) => {
                    if (postQueryFilters.customSort.presentAndUpcoming) {
                        if (!a.actualEndDate || !b.actualEndDate) {
                            if (!a.actualEndDate && !b.actualEndDate) {
                                return new Date(b.createdAt) - new Date(a.createdAt);
                            }
                            if (!a.actualEndDate) return -1;
                            if (!b.actualEndDate) return 1;
                        }
                        
                        return new Date(a.actualEndDate) - new Date(b.actualEndDate);
                    } else {
                        return new Date(b.actualEndDate) - new Date(a.actualEndDate);
                    }
                });
            }

            return sortedEvents.map(({ actualEndDate, ...event }) => event);
        }
    };
};

module.exports = buildEventQuery;