const getRedisClient = require("../infrastructure/redisClient");

const addNotificationToQueue = async(userId, notification) => {
    const redisClient = getRedisClient();
    try {
        const notificationString = JSON.stringify(notification);
        const queueName = `user_notifications:${userId}`;
        await redisClient.rpush(queueName, notificationString);
        console.log(`Notification added to queue for userId: ${userId}`);
    } catch (error) {
        console.error('Error adding notification to queue:', error);
    }
};

const readUserNotificationsFromQueue = async(userId) => {
    const redisClient = getRedisClient();
    try {
        const queueName = `user_notifications:${userId}`;
        console.log(`Reading notifications from queue for userId: ${userId}`);
        const notifications = await redisClient.lrange(queueName, 0, -1);
        
        const parsedNotifications = notifications.map(notif => JSON.parse(notif));
        
        if (notifications.length > 0) {
            await redisClient.ltrim(queueName, notifications.length, -1);
            console.log(`${notifications.length} notifications trimmed from queue for userId: ${userId}`);
        }
    
        return parsedNotifications;
    } catch (error) {
        console.error(`Error reading notifications from Redis queue for userId ${userId}:`, error);
        return [];
    }
};

module.exports = {
    addNotificationToQueue,
    readUserNotificationsFromQueue
};