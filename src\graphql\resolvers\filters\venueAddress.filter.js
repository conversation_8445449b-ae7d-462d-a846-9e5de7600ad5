const buildVenueAddressQuery = (filter) => {
    const query = {};

    if (filter) {
        if (filter.name) {
            query.name = { $regex: filter.name, $options: 'i' };
        }

        if (filter.placeId) {
            query.placeId = { $regex: filter.placeId, $options: 'i' };
        }

        if (filter.address) {
            query.address = { $regex: filter.address, $options: 'i' };
        }

        if (filter.city) {
            query.city = { $regex: filter.city, $options: 'i' };
        }

        if (filter.state) {
            query.state = { $regex: filter.state, $options: 'i' };
        }

        if (filter.buildingDetails) {
            query.buildingDetails = { $regex: filter.buildingDetails, $options: 'i' };
        }

        if (filter.directions) {
            query.directions = { $regex: filter.directions, $options: 'i' };
        }
    }

    return query;
};

module.exports = buildVenueAddressQuery; 