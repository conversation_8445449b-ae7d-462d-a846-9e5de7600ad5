const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    enum UserRole {
        MAIN_HOST
        CO_HOST
        GUEST
    }
    
    type WeatherCondition {
        text: String
        icon: String
    }

    type WeatherCoordinates {
        latitude: Float
        longitude: Float
    }

    type Weather {
        avgTempC: Float
        avgTempF: Float
        condition: WeatherCondition
        coordinates: WeatherCoordinates
        weatherUrl: String
    }

    type Party {
        id: ID!
        name: String!
        partyType: MdPartyType
        time: DateTime
        serviceLocation: MdServiceLocation
        expectedGuestCount: Int!
        actualGuestCount: Int!
        coHosts: [Host!]
        services: [PartyService!]
        vendorTypes: [MdVendorType!]
        totalBudget: Float!
        totalExpenditure: Float!
        guests: [Guest!]
        event: Event!
        weather: Weather
        venueAddress: VenueAddress
        invitation: Invitation
        invitationSettings: InvitationSettings
        rsvps: [InvitationRSVP!]
        activity: [Message!]
        userRole: UserRole
        reminders: [Reminder!]
    }

    input PartyFilterInput {
        name: String
        timeRange: DateTimeRangeInput
        minExpectedGuests: Int
        maxExpectedGuests: Int
        minBudget: Float
        maxBudget: Float
        minExpenditure: Float
        maxExpenditure: Float
        serviceLocation: MdServiceLocationFilterInput
        vendorTypes: MdVendorTypeFilterInput
        eventId: ID
        venueAddress: VenueAddressFilterInput
    }

    type PartyWrapper {
        party: Party!
    }

    type PartiesWrapper {
        parties: [Party]!
    }

    type PartyResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartyWrapper!
    }

    type PartiesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: PartiesWrapper!
        pagination: PaginationInfo!
    }

    type PartyErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union PartyResult = PartyResponse | PartyErrorResponse
    union PartiesResult = PartiesResponse | PartyErrorResponse

    type Query {
        getPartyById(id: ID!): PartyResult!
        getParties(filter: PartyFilterInput, pagination: PaginationInput): PartiesResult!
    }

    input PartyInput {
        name: String!
        partyType: ID
        time: DateTime
        services: [ID!]
        totalBudget: Float
        expectedGuestCount: Int
        serviceLocation: ID
        vendorTypes: [ID!]
        eventId: ID!
        coHosts: [CoHostInput!]
        venueAddress: ID
    }

    input PartyUpdateInput {
        name: String
        partyType: ID
        time: DateTime
        coHosts: [CoHostInput!]
        services: [ID!]
        vendorTypes: [ID!]
        totalBudget: Float
        expectedGuestCount: Int
        serviceLocation: ID
        eventId: ID
        venueAddress: ID
        invitationSettings: InvitationSettingsInput
        invitation: InvitationInput
    }

    input InvitationSettingsInput {
        is_guest_list_public: Boolean
        additional_guest_allowed: Boolean
        additional_guest_limit: Int
        allow_guest_to_add_photo: Boolean
        send_auto_reminder_to_all_guests: Boolean
    }

    input InvitationInput {
        message: String
        media: [ID!]
    }

    type Mutation {
        createParty(input: PartyInput!): PartyResult!
        updateParty(id: ID!, input: PartyUpdateInput!): PartyResult!
        deleteParty(id: ID!): PartyResult!
    }
`; 