const request = require('supertest');
const { User } = require('../../../src/models/User');
const Event = require('../../../src/models/Event');
const Party = require('../../../src/models/Party');
const Task = require('../../../src/models/Task');
const { MdEventType } = require('../../../src/models/MdEventType');
const { MdPartyType } = require('../../../src/models/MdPartyType');
const { MdServiceLocation } = require('../../../src/models/MdServiceLocation');

/**
 * GraphQL test helper
 */
class GraphQLTestHelper {
  constructor(app) {
    this.app = app;
    this.authToken = 'Bearer test-token';
  }

  setAuthToken(token) {
    this.authToken = token;
  }

  async query(query, variables = {}) {
    const response = await request(this.app)
      .post('/graphql')
      .set('Authorization', this.authToken)
      .send({
        query,
        variables
      });
    
    return response;
  }

  async mutation(mutation, variables = {}) {
    return this.query(mutation, variables);
  }
}

/**
 * Test data factory
 */
class TestDataFactory {
  static async createUser(overrides = {}) {
    const userData = {
      role: ['user'],
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+1234567890',
      isRegistered: true,
      emailVerified: true,
      phoneVerified: true,
      externalId: 'test-user-id',
      isActive: true,
      ...overrides
    };

    const user = new User(userData);
    await user.save();
    return user;
  }

  static async createEventType(overrides = {}) {
    const eventTypeData = {
      name: 'Birthday Party',
      description: 'A celebration of birth',
      ...overrides
    };

    const eventType = new MdEventType(eventTypeData);
    await eventType.save();
    return eventType;
  }

  static async createPartyType(overrides = {}) {
    const partyTypeData = {
      name: 'Birthday',
      description: 'Birthday celebration',
      ...overrides
    };

    const partyType = new MdPartyType(partyTypeData);
    await partyType.save();
    return partyType;
  }

  static async createServiceLocation(overrides = {}) {
    const locationData = {
      name: 'Test Location',
      description: 'A test service location',
      ...overrides
    };

    const location = new MdServiceLocation(locationData);
    await location.save();
    return location;
  }

  static async createEvent(user, overrides = {}) {
    const eventType = await this.createEventType();
    const location = await this.createServiceLocation();

    const eventData = {
      eventType: eventType._id,
      name: 'Test Event',
      description: 'A test event',
      startDate: new Date(),
      endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day later
      mainHost: user._id,
      budget: 1000,
      location: location._id,
      eventStatus: 'PLANNED',
      ...overrides
    };

    const event = new Event(eventData);
    await event.save();
    return event;
  }

  static async createParty(event, overrides = {}) {
    const partyType = await this.createPartyType();
    const location = await this.createServiceLocation();

    const partyData = {
      name: 'Test Party',
      partyType: partyType._id,
      time: new Date(),
      serviceLocation: location._id,
      expectedGuestCount: 10,
      actualGuestCount: 0,
      totalBudget: 500,
      totalExpenditure: 0,
      event: event._id,
      ...overrides
    };

    const party = new Party(partyData);
    await party.save();
    return party;
  }

  static async createTask(event, user, overrides = {}) {
    const taskData = {
      title: 'Test Task',
      description: 'A test task',
      assignee: user._id,
      event: event._id,
      status: 'PENDING',
      priority: 'MEDIUM',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week later
      ...overrides
    };

    const task = new Task(taskData);
    await task.save();
    return task;
  }
}

/**
 * Database test utilities
 */
class DatabaseTestUtils {
  static async clearCollection(model) {
    await model.deleteMany({});
  }

  static async clearAllCollections() {
    const collections = [User, Event, Party, Task, MdEventType, MdPartyType, MdServiceLocation];
    await Promise.all(collections.map(collection => this.clearCollection(collection)));
  }

  static async countDocuments(model, filter = {}) {
    return await model.countDocuments(filter);
  }
}

/**
 * Authentication test utilities
 */
class AuthTestUtils {
  static createMockToken(userId = 'test-user-id', roles = ['user']) {
    return `Bearer mock-token-${userId}`;
  }

  static mockClerkVerification(userId = 'test-user-id', roles = ['user']) {
    const clerkUtils = require('../../../src/utils/clerk.util');
    clerkUtils.verifyTokenLocally.mockResolvedValue({
      sub: userId,
      metadata: { role: roles }
    });
    clerkUtils.verifyClerkUser.mockResolvedValue(true);
  }
}

module.exports = {
  GraphQLTestHelper,
  TestDataFactory,
  DatabaseTestUtils,
  AuthTestUtils
};
