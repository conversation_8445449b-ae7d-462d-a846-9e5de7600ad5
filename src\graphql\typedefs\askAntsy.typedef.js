const { gql } = require('apollo-server-express');
const sharedTypeDef = require('./shared.typedef');

const askAntsyTypeDefs = gql`
    ${sharedTypeDef}

    input PartyInfoInput {
        partyId: String!
    }

    input TrafficAnalysisInput {
        lat: Float!
        lng: Float!
        partyId: String!
    }

    type Query {
        traffic(input: TrafficAnalysisInput!): MessageResult!
        weather(input: PartyInfoInput!): MessageResult!
        venueDetails(input: PartyInfoInput!): MessageResult!
        partyDetails(input: PartyInfoInput!): MessageResult!
    }
`;

module.exports = askAntsyTypeDefs;