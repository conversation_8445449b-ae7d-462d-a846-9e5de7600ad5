const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const eventGroupSchema = new Schema({
    name : { type: String, required: true },
    description : { type: String },
    interests : { type: [Schema.Types.ObjectId], ref: 'Interest' },
    interestCategories : { type: [Schema.Types.ObjectId], ref: 'InterestCategory' },
    members : { type: [Schema.Types.ObjectId], ref: 'User', required: true },
    invitedMembers : { type: [Schema.Types.ObjectId], ref: 'User' },
    organizers : { 
        type: [Schema.Types.ObjectId], 
        ref: 'User', 
        required: true,
        validate: {
            validator: function(organizers) {
                return organizers.length <= 3;
            },
            message: 'Maximum 2 co-organizers allowed (excluding creator)'
        }
    },
    parentGroup : { type: Schema.Types.ObjectId, ref: 'EventGroup' },
    type : { type: String, enum: ['CIRCLE', 'ORGANIZATION'], required: true },
    imageUrl : { type: String },
    createdBy : { type: Schema.Types.ObjectId, ref: 'User', required: true },
}, { timestamps: true, collection: 'event_groups' });

const EventGroup = model('EventGroup', eventGroupSchema);

module.exports = EventGroup;
