const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    type VenueAddressCoordinates {
        latitude: Float!
        longitude: Float!
    }

    type VenueAddress {
        id: ID!
        name: String!
        placeId: String!
        address: String!
        city: String!
        state: String!
        buildingDetails: String
        directions: String
        coordinates: VenueAddressCoordinates!
    }

    input VenueAddressFilterInput {
        name: String
        placeId: String
        address: String
        city: String
        state: String
        buildingDetails: String
        directions: String
    }

    type VenueAddressWrapper {
        venueAddress: VenueAddress!
    }

    type VenueAddressesWrapper {
        venueAddresses: [VenueAddress]!
    }

    type VenueAddressResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VenueAddressWrapper!
    }

    type VenueAddressesResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: VenueAddressesWrapper!
        pagination: PaginationInfo!
    }

    type VenueAddressErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union VenueAddressResult = VenueAddressResponse | VenueAddressErrorResponse
    union VenueAddressesResult = VenueAddressesResponse | VenueAddressErrorResponse

    type Query {
        getVenueAddressById(id: ID!): VenueAddressResult!
        getVenueAddresses(filter: VenueAddressFilterInput, pagination: PaginationInput): VenueAddressesResult!
    }

    input VenueAddressInput {
        name: String!
        placeId: String!
        buildingDetails: String
        directions: String
    }

    input VenueAddressUpdateInput {
        name: String
        placeId: String
        buildingDetails: String
        directions: String
    }

    type Mutation {
        createVenueAddress(input: VenueAddressInput!): VenueAddressResult!
        updateVenueAddress(id: ID!, input: VenueAddressUpdateInput!): VenueAddressResult!
        deleteVenueAddress(id: ID!): VenueAddressResult!
    }
`; 