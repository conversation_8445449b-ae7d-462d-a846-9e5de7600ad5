const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const addressBookSchema = new Schema({
    label: {
        type: String,
        trim: true
    },
    user: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    venueAddress: {
        type: Schema.Types.ObjectId,
        ref: 'VenueAddress',
        required: true
    }
}, {
    timestamps: true,
    collection: 'address_book'
});

const AddressBook = model('AddressBook', addressBookSchema);

module.exports = AddressBook; 