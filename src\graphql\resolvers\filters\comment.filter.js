const { isValidObjectId, createSafeRegexQuery } = require("../../../utils/validation.util");
const buildUserQuery = require("./user.filter");
const User = require("../../../models/User");

const buildCommentQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.content) {
            const regexQuery = createSafeRegexQuery(filter.content);
            if (regexQuery) {
                query.content = regexQuery;
            }
        }

        if (filter.createdBy) {
            if (typeof filter.createdBy === 'string') {
                if (isValidObjectId(filter.createdBy)) {
                    query.createdBy = filter.createdBy;
                } else {
                    throw new Error('Invalid createdBy ID provided');
                }
            } else if (typeof filter.createdBy === 'object') {
                const userQuery = await buildUserQuery(filter.createdBy);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.createdBy = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.createdBy = { $in: [] };
                    }
                }
            }
        }

        if (filter.createdAt) {
            query.createdAt = {};
            if (filter.createdAt.start) {
                query.createdAt.$gte = filter.createdAt.start;
            }
            if (filter.createdAt.end) {
                query.createdAt.$lte = filter.createdAt.end;
            }
        }

        if (filter.updatedAt) {
            query.updatedAt = {};
            if (filter.updatedAt.start) {
                query.updatedAt.$gte = filter.updatedAt.start;
            }
            if (filter.updatedAt.end) {
                query.updatedAt.$lte = filter.updatedAt.end;
            }
        }

        if (filter.mentions && filter.mentions.length > 0) {
            const mentionQueries = await Promise.all(
                filter.mentions.map(async (mention) => {
                    if (typeof mention === 'string') {
                        if (isValidObjectId(mention)) {
                            return mention;
                        } else {
                            throw new Error('Invalid mention ID provided');
                        }
                    } else if (typeof mention === 'object') {
                        const userQuery = await buildUserQuery(mention);
                        if (Object.keys(userQuery).length > 0) {
                            const matchingUsers = await User.find(userQuery).select('_id');
                            return matchingUsers.map(user => user._id);
                        }
                    }
                    return [];
                })
            );

            const flattenedMentions = mentionQueries.flat().filter(Boolean);
            if (flattenedMentions.length > 0) {
                query.mentions = { $all: flattenedMentions };
            } else {
                query.mentions = { $in: [] };
            }
        }
    }

    return query;
};

module.exports = buildCommentQuery;
