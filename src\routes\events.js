const express = require('express');
const router = express.Router();
const cors = require('cors');
const getRedisClient = require('../infrastructure/redisClient');
const { isValidJWT, verifyTokenLocally, verifyClerkUser } = require('../utils/clerk.util');
const { getTokenFromHeader } = require('../utils/serverSetup.util');
const { GraphQLError } = require('graphql');
const Party = require('../models/Party');
const { hasEventPartyGuestLevelAccess } = require('../utils/auth/accessLevels.util');
const { User } = require('../models/User');
const { readActivityMessages, addMessageToActivityQueue } = require('../utils/messageQueue.util');

// Enable CORS for all routes in this router
router.use(cors());

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const token = getTokenFromHeader(req);
    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const verifiedToken = await verifyTokenLocally(token);
    await verifyClerkUser(verifiedToken.sub);
    
    // Find and cache the database user ID during authentication
    const dbUser = await User.findOne({ externalId: verifiedToken.sub });
    if (!dbUser) {
      return res.status(401).json({ error: 'User not found in database' });
    }
    
    req.user = {
      ...verifiedToken,
      _id: dbUser._id // Add database ID to the user object
    };
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Authentication failed' });
  }
};

// Keep track of connected clients
// const clients = new Set(); // Old implementation
const clientsByParty = new Map(); // Key: partyId, Value: Set of client response streams (res)
const partyIntervals = new Map(); // Key: partyId, Value: { intervalId: NodeJS.Timeout }

// SSE endpoint
router.get('/', authenticate, async (req, res) => {
  const partyId = req.query.partyId;
  // let counter = 0; // Removed: counter will be managed per party interval

  try {
    // Now we can use req.user._id directly without another DB query
    const party = await Party.findById(partyId);
    if (!party) {
      return res.status(404).json({ error: 'Party not found' });
    }

    const { hasAccess } = await hasEventPartyGuestLevelAccess(req.user._id, party.eventId, partyId);
    if (!hasAccess) {
      return res.status(403).json({ error: 'Unauthorized access to this party' });
    }

    // Set headers for SSE and CORS
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',  // Allow all origins
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'  // Added Authorization to allowed headers
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ type: 'connection', message: 'Connected to event stream' })}\n\n`);

    // Add this client to our Set for the specific party
    if (!clientsByParty.has(partyId)) {
      clientsByParty.set(partyId, new Set());
    }
    clientsByParty.get(partyId).add(res);
    console.log(`Client connected for partyId: ${partyId}. Total clients for party: ${clientsByParty.get(partyId).size}`);
    

    // Set up interval to check for new messages only if one doesn't exist for this party
    if (!partyIntervals.has(partyId)) {
      console.log(`Starting new interval for partyId: ${partyId}`);
      let partyCounter = 0; // Counter for heartbeat logic, specific to this party's interval

      const intervalId = setInterval(async () => {
        const currentPartyClients = clientsByParty.get(partyId);

        // If no clients for this party anymore (e.g., all disconnected before interval properly cleared), stop.
        if (!currentPartyClients || currentPartyClients.size === 0) {
          console.log(`No clients for party ${partyId}, interval ${intervalId} attempting to self-clear.`);
          // Ensure this specific interval instance is the one registered before clearing
          const currentIntervalData = partyIntervals.get(partyId);
          if (currentIntervalData && currentIntervalData.intervalId === intervalId) {
            clearInterval(intervalId);
            partyIntervals.delete(partyId);
            console.log(`Interval for party ${partyId} cleared from within due to no clients.`);
          }
          return;
        }

        try {
          const messages = await readActivityMessages(partyId); // One read per party
          let messagesSentThisTick = false;
          
          if (messages.length > 0) {
            messages.forEach(message => {
              currentPartyClients.forEach(clientRes => {
                try {
                  clientRes.write(`data: ${JSON.stringify(message)}\n\n`);
                } catch (e) {
                  console.error(`Error writing message to client for party ${partyId}. Removing client. Error: ${e.message}`);
                  clientRes.end(); // Ensure problematic client stream is closed
                  currentPartyClients.delete(clientRes); // Remove from set
                }
              });
            });
            partyCounter = 0; // Reset counter for this party
            messagesSentThisTick = true;
          }

          if (!messagesSentThisTick) {
            partyCounter++; 
          }

          if (partyCounter >= 10) { // 30 seconds of no messages for this party
            currentPartyClients.forEach(clientRes => {
              try {
                clientRes.write(`data: ${JSON.stringify({type: "heartbeat", timestamp: Date.now(), partyId})}\n\n`);
              } catch (e) {
                console.error(`Error writing heartbeat to client for party ${partyId}. Removing client. Error: ${e.message}`);
                clientRes.end();
                currentPartyClients.delete(clientRes);
              }
            });
            partyCounter = 0; // Reset counter for this party
          }

        } catch (error) {
          console.error(`Error in message interval for party ${partyId}:`, error);
          // Consider if interval should be stopped on certain errors, e.g., Redis connection error
        }
      }, 1000);
      partyIntervals.set(partyId, { intervalId });
    }

    // Handle client disconnect
    req.on('close', () => {
      // clearInterval(heartbeatInterval); // Old logic
      // clearInterval(messageInterval); // Old logic
      // clients.delete(res); // Old logic

      console.log(`Client disconnecting for partyId: ${partyId}`);
      const partyClientsSet = clientsByParty.get(partyId);
      if (partyClientsSet) {
        partyClientsSet.delete(res);
        console.log(`Client removed for partyId: ${partyId}. Remaining clients for party: ${partyClientsSet.size}`);
        if (partyClientsSet.size === 0) {
          console.log(`Last client for partyId: ${partyId} disconnected. Clearing interval.`);
          clientsByParty.delete(partyId); // Remove party from map
          const intervalData = partyIntervals.get(partyId);
          if (intervalData) {
            clearInterval(intervalData.intervalId);
            partyIntervals.delete(partyId);
            console.log(`Interval cleared for partyId: ${partyId}`);
          }
        }
      }
      res.end(); // Important to end the response stream for the disconnected client
    });

  } catch (error) {
    console.error('Error setting up SSE connection:', error);
    if (!res.headersSent) {
        // Ensure status is set only if headers haven't been sent
        res.status(500).json({ error: 'Failed to set up SSE connection' });
    } else {
        res.end(); // End stream if headers already sent
    }
  }
});

// Helper function to broadcast messages to all connected clients
const broadcastMessage = (message, targetPartyId = null) => {
  if (targetPartyId) {
    const partyClients = clientsByParty.get(targetPartyId);
    if (partyClients) {
      partyClients.forEach(clientRes => {
        try {
          clientRes.write(`data: ${JSON.stringify(message)}\n\n`);
        } catch (error) {
          console.error(`Error broadcasting to client for party ${targetPartyId}:`, error.message);
          clientRes.end(); // Close stream
          partyClients.delete(clientRes); // Remove from set
        }
      });
    }
  } else {
    // Broadcast to all clients across all parties
    clientsByParty.forEach((partyClientsSet, partyId) => {
      partyClientsSet.forEach(clientRes => {
        try {
          clientRes.write(`data: ${JSON.stringify(message)}\n\n`);
        } catch (error) {
          console.error(`Error broadcasting globally to client for party ${partyId}:`, error.message);
          clientRes.end();
          partyClientsSet.delete(clientRes); // Remove from specific party set
        }
      });
    });
  }
};

module.exports = router;