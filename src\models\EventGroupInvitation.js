const mongoose = require('mongoose');
const { Schema, model } = mongoose;

const eventGroupInvitationSchema = new Schema({
    user: { 
        type: Schema.Types.ObjectId, 
        ref: 'User', 
        required: true 
    },
    eventGroup: {
        type: Schema.Types.ObjectId,
        ref: 'EventGroup',
        required: true
    },
    status: { 
        type: String, 
        enum: ['ACCEPTED', 'REJECTED', 'PENDING'], 
        default: 'PENDING',
        required: true 
    }
}, { 
    timestamps: true, 
    collection: 'event_group_invitations' 
});

// Add unique compound index to prevent duplicate invitations
eventGroupInvitationSchema.index({ user: 1, eventGroup: 1 }, { unique: true });

const EventGroupInvitation = model('EventGroupInvitation', eventGroupInvitationSchema);

module.exports = EventGroupInvitation; 