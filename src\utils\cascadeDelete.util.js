const Comment = require('../models/Comment');
const Reaction = require('../models/Reaction');
const Task = require('../models/Task');
const Document = require('../models/Document');
const Media = require('../models/Media');
const Message = require('../models/Message');
const { clearCacheById } = require('./cache.util');
const { deleteFileFromBlob } = require('./azureStorage.util');
const MediaFolder = require('../models/MediaFolder');
const Invitation = require('../models/Invitation');
const InvitationRSVP = require('../models/InvitationRSVP');
const InvitationSettings = require('../models/InvitationSettings');
const Guest = require('../models/Guest');
const TaskCollaborator = require('../models/TaskCollaborator');
const Reminder = require('../models/Reminder');
const PartyService = require('../models/PartyService');
const { deleteMediaAndRelatedData } = require('./media.util');
const { deleteScheduledParty } = require('./scheduleParty.util');
const Party = require('../models/Party');
const Event = require('../models/Event');
const EventGroup = require('../models/EventGroup');
const AddressBook = require('../models/AddressBook');
const VenueAddress = require('../models/VenueAddress');

const cascadeDelete = async (modelName, id, options = {}) => {
    try {
        switch (modelName) {
            case 'AddressBook':
                const addressBook = await AddressBook.findById(id);
                if (!addressBook) return { success: false, error: { message: 'Address book not found' } };

                await AddressBook.findByIdAndDelete(id);

                const venueAddressId = addressBook.venueAddress;
                const partyUsingVenueAddress = await Party.findOne({ venueAddress: venueAddressId });
                
                if (!partyUsingVenueAddress) {
                    await VenueAddress.findByIdAndDelete(venueAddressId);
                }

                await clearCacheById(id);
                return { success: true };

            case 'Document':
                const document = await Document.findById(id);
                if (!document) return { success: false, error: { message: 'Document not found' } };

                if (document.documentUrl) {
                    try {
                        const blobUrl = new URL(document.documentUrl);
                        const pathParts = blobUrl.pathname.split('/');
                        const container = pathParts[1];
                        const blobKey = pathParts[2];

                        await deleteFileFromBlob(blobKey, container);
                    } catch (error) {
                        console.error(`Error deleting file from blob storage: ${error.message}`);
                        return { success: false, error };
                    }
                }

                await Document.findByIdAndDelete(id);
                return { success: true };

            case 'Task':
                const task = await Task.findById(id);
                if (!task) return { success: false, error: { message: 'Task not found' } };

                if (task.attachments && task.attachments.length > 0) {
                    const documents = await Document.find({ _id: { $in: task.attachments } });
                    
                    await Promise.all(documents.map(async (doc) => {
                        if (doc.documentUrl) {
                            try {
                                const blobUrl = new URL(doc.documentUrl);
                                const pathParts = blobUrl.pathname.split('/');
                                const container = pathParts[1];
                                const blobKey = pathParts[2];

                                await deleteFileFromBlob(blobKey, container);
                            } catch (error) {
                                console.error(`Error deleting file from blob storage: ${error.message}`);
                            }
                        }
                    }));

                    await Document.deleteMany({ _id: { $in: task.attachments } });
                    await Promise.all(task.attachments.map(aid => clearCacheById(aid)));
                }

                const comments = await Comment.find({ _id: { $in: task.comments } });
                const reactionIds = comments.reduce((acc, comment) => {
                    return acc.concat(comment.reactions || []);
                }, []);

                await Reaction.deleteMany({ _id: { $in: reactionIds } });
                await Comment.deleteMany({ _id: { $in: task.comments } });
                
                await Promise.all([
                    ...reactionIds.map(rid => clearCacheById(rid)),
                    ...task.comments.map(cid => clearCacheById(cid))
                ]);

                await Task.findByIdAndDelete(id);
                return { success: true };

            case 'Comment':
                const comment = await Comment.findById(id);
                if (!comment) return { success: false, error: { message: 'Comment not found' } };

                if (comment.reactions && comment.reactions.length > 0) {
                    await Reaction.deleteMany({ _id: { $in: comment.reactions } });
                    await Promise.all(comment.reactions.map(rid => clearCacheById(rid)));
                }

                if (comment.task) {
                    await Task.findByIdAndUpdate(comment.task, {
                        $pull: { comments: id }
                    });
                }

                return { success: true };

            case 'Message':
                const message = await Message.findById(id);
                if (!message) return { success: false, error: { message: 'Message not found' } };

                // Recursive function to delete message tree
                const deleteMessageTree = async (messageId) => {
                    const currentMessage = await Message.findById(messageId);
                    
                    // 1. Delete all media
                    if (currentMessage.media && currentMessage.media.length > 0) {
                        // 1.1 Delete from blob storage
                        const mediaItems = await Media.find({ _id: { $in: currentMessage.media } });
                        await Promise.all(mediaItems.map(async (media) => {
                            if (media.url) {
                                try {
                                    const blobUrl = new URL(media.url);
                                    const pathParts = blobUrl.pathname.split('/');
                                    const container = pathParts[1];
                                    const blobKey = pathParts[2];
                                    await deleteFileFromBlob(blobKey, container);

                                } catch (error) {
                                    console.error(`Error deleting media from blob storage: ${error.message}`);
                                }
                            }
                        }));
                        
                        const mediaFolder = await MediaFolder.findOne({ event: currentMessage.eventId });
                        if (mediaFolder) {
                            mediaFolder.media = mediaFolder.media.filter(mid => !currentMessage.media.includes(mid));
                            await mediaFolder.save();
                        }
                        // Delete media documents
                        await Media.deleteMany({ _id: { $in: currentMessage.media } });
                        await Promise.all(currentMessage.media.map(mid => clearCacheById(mid)));
                    }

                    // 2. Delete reactions
                    if (currentMessage.reactions && currentMessage.reactions.length > 0) {
                        await Reaction.deleteMany({ _id: { $in: currentMessage.reactions } });
                        await Promise.all(currentMessage.reactions.map(rid => clearCacheById(rid)));
                    }

                    // 3. Delete all child messages
                    const childMessages = await Message.find({ parentMessageId: messageId });
                    await Promise.all(childMessages.map(child => deleteMessageTree(child._id)));

                    // Finally delete this message
                    await Message.findByIdAndDelete(messageId);
                };

                await deleteMessageTree(id);
                return { success: true };

            case 'Party':
                const party = await Party.findById(id);
                if (!party) return { success: false, error: { message: 'Party not found' } };

                await Promise.all([
                    Task.deleteMany({ party: id }),
                    
                    deleteScheduledParty(party),
                    
                    Invitation.findOneAndDelete({ party: id }).then(async (invitation) => {
                        if (invitation) {
                            await Promise.all([
                                InvitationRSVP.deleteMany({ invitation: invitation._id }),
                                InvitationSettings.deleteOne({ party: id })
                            ]);
                        }
                    }),
                    
                    Guest.find({ party: id }).then(async (guests) => {
                        const guestIds = guests.map(g => g._id);
                        await Promise.all([
                            Guest.deleteMany({ party: id }),
                            TaskCollaborator.deleteMany({ guest: { $in: guestIds } })
                        ]);
                    }),
                    
                    Reminder.deleteMany({ party: id }),
                    Message.deleteMany({ partyId: id }),
                    PartyService.deleteMany({ _id: { $in: party.services || [] } }),
                ]);

                await Party.findByIdAndDelete(id);
                return { success: true };

            case 'Event':
                const event = await Event.findById(id);
                if (!event) return { success: false, error: { message: 'Event not found' } };

                const parties = await Party.find({ eventId: id });
                const partyIds = parties.map(p => p._id);

                await Promise.all([
                    ...partyIds.map(partyId => cascadeDelete('Party', partyId)),

                    MediaFolder.find({ event: id }).then(async (folders) => {
                        await Promise.all(folders.map(async (folder) => {
                            if (folder.media?.length > 0) {
                                await deleteMediaAndRelatedData(folder.media);
                            }
                            await MediaFolder.findByIdAndDelete(folder._id);
                            await clearCacheById(folder._id);
                        }));
                    }),

                    EventGroup.updateMany(
                        { events: id },
                        { $pull: { events: id } }
                    ),

                    Task.deleteMany({ event: id }),
                    Guest.deleteMany({ event: id }),
                    TaskCollaborator.deleteMany({ event: id })
                ]);

                await Event.findByIdAndDelete(id);
                await clearCacheById(id);
                return { success: true };

            default:
                return { success: false, error: { message: 'Unsupported model type' } };
        }
    } catch (error) {
        console.error('Cascade delete error:', error);
        return { success: false, error };
    }
};

module.exports = {
    cascadeDelete
}; 