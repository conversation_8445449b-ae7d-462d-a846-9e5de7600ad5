const Media = require('../models/Media');
const MediaFolder = require('../models/MediaFolder');
const Party = require('../models/Party');
const Host = require('../models/Host');
const Guest = require('../models/Guest');
const { deleteFileFromBlob } = require('./azureStorage.util');
const { clearCacheById } = require('./cache.util');
const VendorUser = require('../models/VendorUser');
const Vendor = require('../models/Vendor');
const PartyService = require('../models/PartyService');
const Event = require('../models/Event');

const RESERVED_FOLDER_NAMES = ['archive', 'favourites', 'collage', 'highlight vedio', 'animation', 'my archives album'];

const validateEventCollaborator = async (event, userId) => {
    if (!event || !userId) return false;

    const hostRecord = await Host.findById(userId);
    if (hostRecord) {
        userId = hostRecord.userId;
    }

    const mainHostRecord = await Host.findById(event.mainHost);
    const isMainHost = mainHostRecord && mainHostRecord.userId.toString() === userId.toString();

    const parties = await Party.find({ eventId: event._id });
    
    const allCoHostIds = [
        ...(event.coHosts || []),
        ...parties.reduce((ids, party) => [...ids, ...(party.coHosts || [])], [])
    ];

    const coHostRecords = await Host.find({ _id: { $in: allCoHostIds } });
    
    const isCoHost = coHostRecords.some(host => 
        host.userId && host.userId.toString() === userId.toString()
    );

    const isGuest = await Guest.exists({
        party: { $in: parties.map(p => p._id) },
        user: userId
    });

    const isVendor = await VendorUser.findOne({ user: userId }) && 
        await PartyService.findOne({
            vendor: { 
                $in: await Vendor.find({ 
                    $or: [
                        { primaryContact: await VendorUser.find({ user: userId }).select('_id') },
                        { contacts: await VendorUser.find({ user: userId }).select('_id') }
                    ]
                }).select('_id')
            },
            party: { $in: parties.map(p => p._id) }
        });
    
    return Boolean(isMainHost || isCoHost || isGuest || isVendor);
};

const moveMediaToArchive = async (mediaId, userId) => {
    try {
        let archiveFolder = await MediaFolder.findOne({
            owner: userId,
            category: 'ALBUM',
            name: 'Archive'
        });

        if (!archiveFolder) {
            archiveFolder = new MediaFolder({
                name: 'Archive',
                owner: userId,
                category: 'ALBUM',
                publish: false,
                albumCover: mediaId
            });
            await archiveFolder.save();
        }

        await MediaFolder.findByIdAndUpdate(
            archiveFolder._id,
            { 
                $addToSet: { media: mediaId },
                $set: { albumCover: mediaId }
            }
        );

        await clearCacheById(archiveFolder._id);
        return archiveFolder;
    } catch (error) {
        console.error('Error in moveMediaToArchive:', error);
        throw error;
    }
};

const deleteMediaAndRelatedData = async (mediaId) => {
    try {
        const media = await Media.findById(mediaId);
        if (!media) {
            throw new Error('Media not found');
        }

        await MediaFolder.updateMany(
            { 
                $or: [
                    { name: { $regex: '^MY FAVORITES ALBUM$', $options: 'i' } },
                    { name: { $regex: '^MY FAVOURITES ALBUM$', $options: 'i' } }
                ]
            },
            { $pull: { media: mediaId } }
        );

        const mediaUrl = media.url;
        const urlParts = mediaUrl.split('/');
        const key = urlParts[urlParts.length - 1];
        const containerName = urlParts[urlParts.length - 2];

        try {
            await deleteFileFromBlob(key, containerName);
        } catch (blobError) {
            if (blobError.code === 'BlobNotFound') {
                console.warn(`Blob ${key} already deleted or not found in container ${containerName}`);
            } else {
                throw blobError;
            }
        }

        await Media.findByIdAndDelete(mediaId);
        await clearCacheById(mediaId);

        return true;
    } catch (error) {
        console.error('Error in deleteMediaAndRelatedData:', error);
        throw error;
    }
};

const validateFolderName = (name) => {
    const errors = [];

    if (!name || typeof name !== 'string') {
        errors.push({
            field: 'name',
            message: 'Folder name is required and must be a string'
        });
        return errors;
    }

    if (RESERVED_FOLDER_NAMES.some(reserved => name.toLowerCase() === reserved.toLowerCase())) {
        errors.push({
            field: 'name',
            message: 'This folder name is reserved and cannot be used'
        });
    }

    return errors;
};

const validateEventCollaborators = async (event, input) => {
    const errors = [];
    
    const ownerToCheck = input.owner;
    if (ownerToCheck) {
        const isOwnerCollaborator = await validateEventCollaborator(event, ownerToCheck);
        if (!isOwnerCollaborator) {
            errors.push({ 
                field: 'owner', 
                message: 'Owner must be a collaborator of the associated event' 
            });
        }
    }

    if (input.contributors?.length > 0) {
        for (const contributorId of input.contributors) {
            const isContributorCollaborator = await validateEventCollaborator(event, contributorId);
            if (!isContributorCollaborator) {
                errors.push({ 
                    field: 'contributors', 
                    message: `User ${contributorId} must be a collaborator of the associated event to be added as a contributor` 
                });
            }
        }
    }

    if (input.sharedWith?.length > 0) {
        for (const sharedUserId of input.sharedWith) {
            const isSharedUserCollaborator = await validateEventCollaborator(event, sharedUserId);
            if (!isSharedUserCollaborator) {
                errors.push({ 
                    field: 'sharedWith', 
                    message: `User ${sharedUserId} must be a collaborator of the associated event to be in shared with` 
                });
            }
        }
    }

    return errors;
};

const validateImageUrl = async (url) => {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        const contentType = response.headers.get('content-type');
        return contentType && contentType.startsWith('image/');
    } catch (error) {
        console.error('Error validating image URL:', error);
        return false;
    }
};

const validateAlbumCover = async (albumCover) => {
    if (!albumCover) return null;

    if (!Array.isArray(albumCover)) {
        return {
            field: 'albumCover',
            message: 'Album cover must be an array'
        };
    }

    if (albumCover.length > 2) {
        return {
            field: 'albumCover',
            message: 'Maximum of 2 album cover images allowed'
        };
    }

    const imageValidations = await Promise.all(
        albumCover.map(url => validateImageUrl(url))
    );

    if (imageValidations.some(isValid => !isValid)) {
        return {
            field: 'albumCover',
            message: 'All album cover URLs must be valid image files'
        };
    }

    return null;
};

const updateMediaFolderOwners = async (entityId, entityType, mainHostId, coHostIds = []) => {
    try {
        let ownerIds = [];
        
        if (entityType === 'event') {
            const host = await Host.findById(mainHostId);
            if (!host) {
                throw new Error('Host not found');
            }
            ownerIds = [host.userId];

            if (coHostIds.length > 0) {
                const coHosts = await Host.find({ _id: { $in: coHostIds } });
                ownerIds.push(...coHosts.map(coHost => coHost.userId));
            }

            const query = { event: entityId };
            const mediaFolder = await MediaFolder.findOne(query);

            if (mediaFolder) {
                mediaFolder.owner = ownerIds;
                await mediaFolder.save();
                await clearCacheById(mediaFolder._id);
            }
        } else if (entityType === 'party') {
            const event = await Event.findById(mainHostId);
            if (!event) {
                throw new Error('Event not found');
            }

            const mainHost = await Host.findById(event.mainHost);
            ownerIds = [mainHost.userId];

            const eventCoHosts = await Host.find({ _id: { $in: event.coHosts || [] } });
            ownerIds.push(...eventCoHosts.map(coHost => coHost.userId));

            if (coHostIds.length > 0) {
                const partyCoHosts = await Host.find({ _id: { $in: coHostIds } });
                ownerIds.push(...partyCoHosts.map(coHost => coHost.userId));
            }

            const query = { party: entityId };
            const mediaFolder = await MediaFolder.findOne(query);

            if (mediaFolder) {
                mediaFolder.owner = [...new Set(ownerIds)]; 
                await mediaFolder.save();
                await clearCacheById(mediaFolder._id);
            }
        }

        return ownerIds;
    } catch (error) {
        console.error('Error in updateMediaFolderOwners:', error);
        throw error;
    }
};

const getArchivesAlbum = async (userId) => {
    let archivesAlbum = await MediaFolder.findOne({
        name: 'MY ARCHIVES ALBUM',
        owner: userId,
        category: 'ALBUM'
    });
    if (!archivesAlbum) {
        archivesAlbum = new MediaFolder({
            name: 'MY ARCHIVES ALBUM',
            owner: userId,
            category: 'ALBUM'
        });
        await archivesAlbum.save();
    }
    return archivesAlbum;
};

module.exports = {
    deleteMediaAndRelatedData,
    moveMediaToArchive,
    validateEventCollaborator,
    validateFolderName,
    validateEventCollaborators,
    validateImageUrl,
    validateAlbumCover,
    updateMediaFolderOwners,
    getArchivesAlbum
}; 