const { isValidObjectId, createSafeRegexQuery } = require('../../../utils/validation.util');
const buildPartyQuery = require('./party.filter');
const buildUserQuery = require('./user.filter');
const buildTagQuery = require('./tag.filter');
const Party = require('../../../models/Party');
const { User } = require('../../../models/User');
const Tag = require('../../../models/Tag');

const buildMediaQuery = async (filter) => {
    const query = {};

    if (filter) {
        if (filter.title) {
            const regexQuery = createSafeRegexQuery(filter.title);
            if (regexQuery) {
                query.title = regexQuery;
            }
        }

        if (filter.partyId) {
            if (isValidObjectId(filter.partyId)) {
                query.party = filter.partyId;
            } else {
                throw new Error('Invalid party ID provided');
            }
        }

        if (filter.party && !filter.partyId) {
            if (typeof filter.party === 'string') {
                if (isValidObjectId(filter.party)) {
                    query.party = filter.party;
                } else {
                    throw new Error('Invalid party ID provided');
                }
            } else if (typeof filter.party === 'object') {
                const { query: partyQuery, applyPostQueryFilters } = await buildPartyQuery(filter.party);
                if (Object.keys(partyQuery).length > 0) {
                    const matchingParties = await Party.find(partyQuery);
                    const filteredParties = await applyPostQueryFilters(matchingParties);

                    if (filteredParties.length > 0) {
                        query.party = { $in: filteredParties.map(party => party._id) };
                    } else {
                        query.party = { $in: [] };
                    }
                }
            }
        }

        if (filter.uploadedAt) {
            query.uploadedAt = {};
            if (filter.uploadedAt.start) {
                query.uploadedAt.$gte = filter.uploadedAt.start;
            }
            if (filter.uploadedAt.end) {
                query.uploadedAt.$lte = filter.uploadedAt.end;
            }
        }

        if (filter.ownerId) {
            if (isValidObjectId(filter.ownerId)) {
                query.owner = filter.ownerId;
            } else {
                throw new Error('Invalid owner ID provided');
            }
        }

        if (filter.owner && !filter.ownerId) {
            if (typeof filter.owner === 'string') {
                if (isValidObjectId(filter.owner)) {
                    query.owner = filter.owner;
                } else {
                    throw new Error('Invalid owner ID provided');
                }
            } else if (typeof filter.owner === 'object') {
                const userQuery = await buildUserQuery(filter.owner);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.owner = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.owner = { $in: [] };
                    }
                }
            }
        }

        if (filter.tags) {
            if (typeof filter.tags === 'string') {
                if (isValidObjectId(filter.tags)) {
                    query.tags = filter.tags;
                } else {
                    throw new Error('Invalid tag ID provided');
                }
            } else if (Array.isArray(filter.tags)) {
                const validTags = filter.tags.every(tagId => isValidObjectId(tagId));
                if (!validTags) {
                    throw new Error('Invalid tag ID provided in tags array');
                }
                query.tags = { $all: filter.tags };
            } else if (typeof filter.tags === 'object') {
                const tagQuery = await buildTagQuery(filter.tags);
                if (Object.keys(tagQuery).length > 0) {
                    const matchingTags = await Tag.find(tagQuery).select('_id');
                    if (matchingTags.length > 0) {
                        query.tags = { $in: matchingTags.map(tag => tag._id) };
                    } else {
                        query.tags = { $in: [] };
                    }
                }
            }
        }
    }

    return query;
};

module.exports = buildMediaQuery;