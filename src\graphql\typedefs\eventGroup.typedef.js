const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

const eventGroupTypeDef = gql`
    ${sharedTypeDef}

    enum EventGroupType {
        CIRCLE
        ORGANIZATION
    }

    enum LoggedInUserRole {
        MEMBER
        ORGANIZER
        CO_ORGANIZER
    }

    type EventGroup {
        id: ID!
        name: String!
        description: String
        interests: [MdInterest!]
        interestCategories: [MdInterestCategory!]
        members: [User!]
        invitedMembers: [User!]
        membersCount: Int
        organizers: [User!]
        parentGroup: EventGroup
        type: EventGroupType!
        imageUrl: String
        createdBy: User!
        createdAt: DateTime!
        userRole: LoggedInUserRole
    }

    type EventGroupMember {
        user: User!
        role: LoggedInUserRole!
    }

    type EventGroupMembersResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: [EventGroupMember!]!
        pagination: PaginationInfo!
    }

    union EventGroupMembersResult = EventGroupMembersResponse | EventGroupErrorResponse

    type EventGroupWrapper {
        eventGroup: EventGroup!
    }

    type EventGroupsWrapper {
        eventGroups: [EventGroup]!
    }

    type EventGroupResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventGroupWrapper!
    }

    type EventGroupsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: EventGroupsWrapper!
        pagination: PaginationInfo!
    }

    type EventGroupErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union EventGroupResult = EventGroupResponse | EventGroupErrorResponse
    union EventGroupsResult = EventGroupsResponse | EventGroupErrorResponse

    input EventGroupFilterInput {
        name: String
        description: String
        interests: [MdInterestFilterInput!]
        interestCategories: [MdInterestCategoryFilterInput!]
        members: [UserFilterInput!]
        organizers: [UserFilterInput!]
        parentGroup: EventGroupFilterInput
        type: EventGroupType
        createdBy: UserFilterInput
    }

    input EventGroupMemberFilterInput {
        role: LoggedInUserRole
        user: UserFilterInput
    }
     
    type Query {
        getEventGroupById(id: ID!): EventGroupResult!
        getEventGroups(filter: EventGroupFilterInput, pagination: PaginationInput): EventGroupsResult!
        getEventGroupMembers(eventGroupId: ID!, filter: EventGroupMemberFilterInput, pagination: PaginationInput): EventGroupMembersResult!
    }

    input MemberInput {
        firstName: String!
        lastName: String
        phone: String!
        email: String
    }

    input EventGroupInput {
        name: String!
        description: String
        interests: [ID!]
        interestCategories: [ID!]
        organizers: [MemberInput!]
        parentGroup: ID
        type: EventGroupType!
        imageUrl: String
    }

    input EventGroupUpdateInput {
        name: String
        description: String
        interests: [ID!]
        interestCategories: [ID!]
        members: [MemberInput!]
        organizers: [MemberInput!]
        parentGroup: ID
        type: EventGroupType
        imageUrl: String
    }

    extend type Mutation {
        createEventGroup(input: EventGroupInput!): EventGroupResult!
        updateEventGroup(id: ID!, input: EventGroupUpdateInput!): EventGroupResult!
        deleteEventGroup(id: ID!): EventGroupResult!
    }    
`

module.exports = eventGroupTypeDef;