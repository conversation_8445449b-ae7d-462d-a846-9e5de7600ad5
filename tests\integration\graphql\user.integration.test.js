const { Graph<PERSON><PERSON>est<PERSON>el<PERSON>, TestDataFactory, AuthTestUtils } = require('../utils/testHelpers');

describe('User GraphQL Integration Tests', () => {
  let graphqlHelper;
  let testUser;

  beforeAll(() => {
    graphqlHelper = new GraphQLTestHelper(global.testApp);
  });

  beforeEach(async () => {
    // Create a test user for authenticated operations
    testUser = await TestDataFactory.createUser();
    AuthTestUtils.mockClerkVerification(testUser.externalId, testUser.role);
  });

  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      const registerMutation = `
        mutation Register($input: RegisterInput!) {
          register(input: $input) {
            ... on UserResponse {
              status
              message
              result {
                user {
                  id
                  firstName
                  lastName
                  email
                  phone
                  role
                  isRegistered
                }
              }
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          role: ['user'],
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          isRegistered: true,
          externalId: 'new-user-external-id'
        }
      };

      const response = await graphqlHelper.mutation(registerMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.register.status).toBe('SUCCESS');
      expect(response.body.data.register.result.user.firstName).toBe('John');
      expect(response.body.data.register.result.user.email).toBe('<EMAIL>');
      expect(response.body.data.register.result.user.role).toContain('user');
    });

    it('should handle registration with missing required fields', async () => {
      const registerMutation = `
        mutation Register($input: RegisterInput!) {
          register(input: $input) {
            ... on UserResponse {
              status
              message
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          role: ['user'],
          // Missing firstName and phone (required fields)
          email: '<EMAIL>',
          isRegistered: true
        }
      };

      const response = await graphqlHelper.mutation(registerMutation, variables);

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('User Queries', () => {
    it('should get current user information', async () => {
      const getUserQuery = `
        query GetUser {
          getUser {
            ... on UserResponse {
              status
              message
              result {
                user {
                  id
                  firstName
                  lastName
                  email
                  phone
                  role
                  isRegistered
                  emailVerified
                  phoneVerified
                  isActive
                }
              }
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getUserQuery);

      expect(response.status).toBe(200);
      expect(response.body.data.getUser.status).toBe('SUCCESS');
      expect(response.body.data.getUser.result.user.firstName).toBe(testUser.firstName);
      expect(response.body.data.getUser.result.user.email).toBe(testUser.email);
    });

    it('should get users with filters and pagination', async () => {
      // Create additional test users
      await TestDataFactory.createUser({ 
        firstName: 'Alice', 
        email: '<EMAIL>',
        externalId: 'alice-id'
      });
      await TestDataFactory.createUser({ 
        firstName: 'Bob', 
        email: '<EMAIL>',
        externalId: 'bob-id'
      });

      const getUsersQuery = `
        query GetUsers($filters: UserFilterInput, $pagination: PaginationInput) {
          getUsers(filters: $filters, pagination: $pagination) {
            ... on UsersResponse {
              status
              message
              result {
                users {
                  id
                  firstName
                  email
                }
              }
              pagination {
                totalItems
                totalPages
                currentPage
                pageSize
              }
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        filters: {
          isRegistered: true
        },
        pagination: {
          limit: 10,
          skip: 0
        }
      };

      const response = await graphqlHelper.query(getUsersQuery, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.getUsers.status).toBe('SUCCESS');
      expect(response.body.data.getUsers.result.users).toHaveLength(3); // testUser + Alice + Bob
      expect(response.body.data.getUsers.pagination.totalItems).toBe(3);
    });
  });

  describe('User Updates', () => {
    it('should update user information', async () => {
      const updateUserMutation = `
        mutation UpdateUser($input: UserUpdateInput!) {
          updateUser(input: $input) {
            ... on UserResponse {
              status
              message
              result {
                user {
                  id
                  firstName
                  lastName
                  email
                  phone
                }
              }
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          firstName: 'Updated',
          lastName: 'Name',
          email: '<EMAIL>'
        }
      };

      const response = await graphqlHelper.mutation(updateUserMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.updateUser.status).toBe('SUCCESS');
      expect(response.body.data.updateUser.result.user.firstName).toBe('Updated');
      expect(response.body.data.updateUser.result.user.lastName).toBe('Name');
      expect(response.body.data.updateUser.result.user.email).toBe('<EMAIL>');
    });

    it('should verify user email/phone', async () => {
      const verifyUserMutation = `
        mutation VerifyUser($input: VerifyUserInput!) {
          verifyUser(input: $input) {
            ... on UserResponse {
              status
              message
              result {
                user {
                  id
                  emailVerified
                  phoneVerified
                }
              }
            }
            ... on UserErrorResponse {
              status
              message
              errors {
                field
                message
              }
            }
          }
        }
      `;

      const variables = {
        input: {
          email: testUser.email
        }
      };

      const response = await graphqlHelper.mutation(verifyUserMutation, variables);

      expect(response.status).toBe(200);
      expect(response.body.data.verifyUser.status).toBe('SUCCESS');
    });
  });

  describe('Authentication', () => {
    it('should require authentication for protected queries', async () => {
      // Remove auth token
      graphqlHelper.setAuthToken('');

      const getUserQuery = `
        query GetUser {
          getUser {
            ... on UserResponse {
              status
              message
            }
          }
        }
      `;

      const response = await graphqlHelper.query(getUserQuery);

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].extensions.code).toBe('UNAUTHENTICATED');
    });
  });
});
