const { isValidObjectId } = require('mongoose');
const { User } = require('../../../models/User');
const EventGroup = require('../../../models/EventGroup');
const buildUserQuery = require('./user.filter');
const buildEventGroupQuery = require('./eventGroup.filter');

const buildEventGroupInvitationQuery = async (filters) => {
    const query = {};

    if (filters) {
        if (filters.user) {
            if (typeof filters.user === 'string') {
                if (isValidObjectId(filters.user)) {
                    query.user = filters.user;
                } else {
                    throw new Error('Invalid user ID provided');
                }
            } else if (typeof filters.user === 'object') {
                const userQuery = await buildUserQuery(filters.user);
                if (Object.keys(userQuery).length > 0) {
                    const matchingUsers = await User.find(userQuery).select('_id');
                    if (matchingUsers.length > 0) {
                        query.user = { $in: matchingUsers.map(user => user._id) };
                    } else {
                        query.user = { $in: [] };
                    }
                }
            }
        }

        if (filters.eventGroupId) {
            if (isValidObjectId(filters.eventGroupId)) {
                query.eventGroup = filters.eventGroupId;
            } else {
                throw new Error('Invalid event group ID provided');
            }
        }

        if (filters.eventGroup) {
            if (typeof filters.eventGroup === 'string') {
                if (isValidObjectId(filters.eventGroup)) {
                    query.eventGroup = filters.eventGroup;
                } else {
                    throw new Error('Invalid event group ID provided');
                }
            } else if (typeof filters.eventGroup === 'object') {
                const eventGroupQuery = await buildEventGroupQuery(filters.eventGroup);
                if (Object.keys(eventGroupQuery).length > 0) {
                    const matchingGroups = await EventGroup.find(eventGroupQuery).select('_id');
                    if (matchingGroups.length > 0) {
                        query.eventGroup = { $in: matchingGroups.map(group => group._id) };
                    } else {
                        query.eventGroup = { $in: [] };
                    }
                }
            }
        }

        if (filters.status) {
            query.status = filters.status;
        }

        if (filters.createdAt) {
            query.createdAt = {};
            if (filters.createdAt.start) {
                query.createdAt.$gte = filters.createdAt.start;
            }
            if (filters.createdAt.end) {
                query.createdAt.$lte = filters.createdAt.end;
            }
        }

        if (filters.updatedAt) {
            query.updatedAt = {};
            if (filters.updatedAt.start) {
                query.updatedAt.$gte = filters.updatedAt.start;
            }
            if (filters.updatedAt.end) {
                query.updatedAt.$lte = filters.updatedAt.end;
            }
        }
    }

    return query;
};

module.exports = buildEventGroupInvitationQuery; 