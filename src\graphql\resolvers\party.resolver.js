const Party = require('../../models/Party');
const MdPartyType = require('../../models/MdPartyType');
const Address = require('../../models/Address');
const Host = require('../../models/Host');
const PartyService = require('../../models/PartyService');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildPartyQuery = require('./filters/party.filter');
const { validateReferences } = require('../../utils/validation.util');
const Guest = require('../../models/Guest');
const MdServiceLocation = require('../../models/MdServiceLocation');
const { findReferences } = require('../../utils/referenceCheck.util');
const Event = require('../../models/Event');
const mongoose = require('mongoose');
const { User } = require('../../models/User');
const MdVendorType = require('../../models/MdVendorType');
const { processCoHosts } = require('../../utils/coHost.util');
const { getWeatherForecast } = require('../../utils/weather.util');
const { createTasksFromChecklist } = require('../../utils/taskCreation.util');
const Task = require('../../models/Task');
const MediaFolder = require('../../models/MediaFolder');
const { updateMediaFolderOwners } = require('../../utils/media.util');
const VenueAddress = require('../../models/VenueAddress');
const Invitation = require('../../models/Invitation');
const InvitationSettings = require('../../models/InvitationSettings');
const InvitationRSVP = require('../../models/InvitationRSVP');
const { hasEventPartyLevelAccess, hasEventLevelAccess, hasGuestLevelAccess } = require('../../utils/auth/accessLevels.util');
const Message = require('../../models/Message');
const Reminder = require('../../models/Reminder');
const { notifyPartyUpdate, notifyPartyCancellation, notifyCoHostAdded } = require('../../notification/partyNotification');
const { scheduleParty, deleteScheduledParty } = require('../../utils/scheduleParty.util');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');

const basePartyIdSchema = {
    partyType: { type: 'single', required: false, model: MdPartyType },
    services: { type: 'array', required: false, model: PartyService },
    serviceLocation: { type: 'single', required: false, model: MdServiceLocation },
    venueAddress: { type: 'single', required: false, model: VenueAddress },
    eventId: { type: 'single', model: Event }
};

const partyIdSchema = { 
    ...basePartyIdSchema,
    coHosts: { type: 'array', required: false, model: Host }
};

const createPartyIdSchema = {
    ...basePartyIdSchema,
    partyType: { ...basePartyIdSchema.partyType, required: false },
    eventId: { ...basePartyIdSchema.eventId, required: true }
};

const validatePartyInput = (input) => {
    const errors = [];
    
    if (!input.name?.trim()) {
        errors.push({ field: 'name', message: 'Party name is required' });
    }

    if (input.time) {
        const partyTime = input.time instanceof Date ? input.time : new Date(input.time);

        if (isNaN(partyTime.getTime())) {
            errors.push({ field: 'time', message: 'Invalid party time format' });
        } else if (partyTime < new Date()) {
            errors.push({ field: 'time', message: 'Party time cannot be in the past' });
        }
    }

    return errors;
};

const partyResolvers = {
    Party: {
        invitation: async (parent) => {
            try {
                return await Invitation.findOne({ party: parent._id });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting invitation');
            }
        },
        invitationSettings: async (parent) => {
            try {
                return await InvitationSettings.findOne({ party: parent._id });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting invitation settings');
            }
        },
        rsvps: async (parent) => {
            try {
                return await InvitationRSVP.find({ invitation: parent.invitation });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting RSVPs');
            }
        },
        partyType: async (parent) => {
            try {
                if (!parent.partyType) return null;
                return await MdPartyType.findById(parent.partyType);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party type');
            }
        },

        coHosts: async (parent) => {
            try {
                return await Host.find({ _id: { $in: parent.coHosts } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting co-hosts');
            }
        },

        services: async (parent) => {
            try {
                return await PartyService.find({ _id: { $in: parent.services } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party services');
            }
        },

        guests: async (parent) => {
            try {
                const guests = await Guest.find({ party: parent._id });
                return guests || [];
            } catch (error) {
                console.error(error);
                throw new Error('Error getting guests');
            }
        },

        actualGuestCount: async (parent) => {
            try {
                const guests = await Guest.find({ party: parent._id });
                return guests.length + guests.reduce((total, guest) => total + (guest.additionalGuestsCount || 0), 0);
            } catch (error) {
                console.error(error);
                throw new Error('Error counting guests');
            }
        },

        expectedGuestCount: async (parent) => {
            try {
                return await Guest.countDocuments({ party: parent._id });
            } catch (error) {
                console.error(error);
                throw new Error('Error counting expected guests');
            }
        },
        
        totalExpenditure: async (parent) => {
            try {
                const partyServices = await PartyService.find({ _id: { $in: parent.services } });
                return partyServices.reduce((total, service) => total + (service.expenditure || 0), 0);
            } catch (error) {
                console.error(error);
                throw new Error('Error calculating total expenditure');
            }
        },

        serviceLocation: async (parent) => {
            try {
                if (!parent.serviceLocation) return null;
                return await MdServiceLocation.findById(parent.serviceLocation);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting service location');
            }
        },

        event: async (parent) => {
            try {
                return await Event.findById(parent.eventId);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event');
            }
        },

        vendorTypes: async (parent) => {
            try {
                return await MdVendorType.find({ _id: { $in: parent.vendorTypes } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting vendor types');
            }
        },

        weather: async (parent) => {
            try {
                if (!parent.venueAddress && !parent.serviceLocation) {
                    console.log('Both venue address and service location are missing for party:', parent._id);
                    return null;
                }

                const venueAddress = await VenueAddress.findById(parent.venueAddress);
                let weather

                if (!venueAddress?.address) {
                    const serviceLocation = await MdServiceLocation.findById(parent.serviceLocation);
                
                    if (!serviceLocation?.city) {
                        console.log('No city found for service location:', parent.serviceLocation);
                        return null;
                    }

                    weather = await getWeatherForecast(serviceLocation.city);
                }

                else {
                    weather = await getWeatherForecast(venueAddress.city);
                }
                
                if (!weather?.forecast?.forecastday?.[0]?.day || !weather?.location) {
                    console.log('Invalid weather data format received');
                    return null;
                }

                const lat = weather.location.lat;
                const lon = weather.location.lon;
                const weatherUrl = `https://weather.com/weather/today/l/${lat},${lon}`;

                return {
                    avgTempC: weather.forecast.forecastday[0].day.avgtemp_c,
                    avgTempF: weather.forecast.forecastday[0].day.avgtemp_f,
                    condition: weather.forecast.forecastday[0].day.condition,
                    coordinates: {
                        latitude: lat,
                        longitude: lon
                    },
                    weatherUrl: weatherUrl
                };
            } catch (error) {
                console.error('Weather error:', error);
                return null;
            }
        },

        venueAddress: async (parent) => {
            try {
                if (!parent.venueAddress) return null;
                return await VenueAddress.findById(parent.venueAddress);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting venue address');
            }
        },

        activity: async (parent, _, context) => {
            try {
                const messages = await Message.find({ _id: { $in: parent.activity } });
                const filteredMessages = messages.filter(message => 
                    !message.receivers?.length ||
                    message.receivers.includes(context.user._id)
                );
                return filteredMessages;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting activity');
            }
        },

        userRole: async (parent, _, context) => {
            try {
                if (!context.user?._id) return null;

                // Get the event to check main host and co-hosts
                const event = await Event.findById(parent.eventId);
                if (!event) return null;

                // Get user's host record
                const userHost = await Host.find({ userId: context.user._id });
                const userHostIds = userHost.map(h => h._id);

                // Check if user is main host of the event
                if (event.mainHost.toString() === userHostIds[0]?.toString()) {
                    return 'MAIN_HOST';
                }

                // Check if user is co-host of the party
                if (parent.coHosts?.some(hostId => userHostIds.some(uId => uId.toString() === hostId.toString()))) {
                    return 'CO_HOST';
                }

                // Check if user is a guest
                const isGuest = await Guest.exists({ 
                    party: parent._id,
                    user: context.user._id
                });

                if (isGuest) {
                    return 'GUEST';
                }

                return null;
            } catch (error) {
                console.error('Error determining user role:', error);
                return null;
            }
        },

        reminders: async (parent) => {
            try {
                return await Reminder.find({ party: parent._id });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting reminders');
            }
        }
    },

    Query: {
        getPartyById: async (_, { id }, context) => {
            try {
                const party = await Party.findById(id);
                if (!party) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                const hasAccess = await hasGuestLevelAccess(context.user._id, party.eventId);
                if (!hasAccess) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                    });
                }
                
                let cachedParty = await getByIdCache(id);
                if (!cachedParty) {
                    await setCache(id, party);
                    cachedParty = party;
                }

                return createResponse('PartyResponse', 'SUCCESS', 'Party retrieved successfully', {
                    result: { party: cachedParty }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyErrorResponse', 'FAILURE', 'Error retrieving party', {
                    errors: [{ field: 'getPartyById', message: error.message }]
                });
            }
        },

        getParties: async (_, { filter, pagination }, context) => {
            try {
                if (!context.user?._id) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Authentication required', {
                        errors: [{ field: 'auth', message: 'User must be logged in' }]
                    });
                }

                const { query, applyPostQueryFilters } = await buildPartyQuery(filter);
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const userHosts = await Host.find({ userId: context.user._id }).select('_id');
                const userHostIds = userHosts.map(h => h._id);

                const accessQuery = {
                    ...query,
                    $or: [
                        { 
                            _id: { 
                                $in: (await Guest.find({ user: context.user._id }).select('party')).map(g => g.party) 
                            } 
                        },
                        { 
                            eventId: { 
                                $in: (await Event.find({
                                    $or: [
                                        { mainHost: { $in: userHostIds } },
                                        { coHosts: { $in: userHostIds } }
                                    ]
                                }).select('_id')).map(e => e._id)
                            } 
                        },
                        { coHosts: { $in: userHostIds } }
                    ]
                };

                const paginationInfo = await getPaginationInfo(Party, accessQuery, limit, skip);

                let parties = await Party.find(accessQuery)
                    .skip(skip)
                    .limit(limit);

                parties = await applyPostQueryFilters(parties);

                const updatedPaginationInfo = {
                    ...paginationInfo,
                    totalItems: paginationInfo.totalItems || 0,
                    totalPages: Math.ceil((paginationInfo.totalItems || 0) / limit)
                };

                if (parties.length === 0) {
                    return createResponse('PartiesResponse', 'FAILURE', 'No parties found', {
                        result: { parties },
                        pagination: updatedPaginationInfo
                    });
                }

                return createResponse('PartiesResponse', 'SUCCESS', 'Parties retrieved successfully', {
                    result: { parties },
                    pagination: updatedPaginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyErrorResponse', 'FAILURE', 'Error retrieving parties', {
                    errors: [{ field: 'getParties', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createParty: async (_, { input }, context) => {
            try {
                if (!await hasEventLevelAccess(context.user._id, input.eventId)) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const referenceValidationErrors = await validateReferences(input, createPartyIdSchema, 'Party');
                if (referenceValidationErrors) {
                    const errors = Array.isArray(referenceValidationErrors) 
                        ? referenceValidationErrors 
                        : [{ 
                            field: referenceValidationErrors.field || 'createParty',
                            message: referenceValidationErrors.message || 'Invalid reference'
                        }];

                    return createResponse('PartyErrorResponse', 'FAILURE', 'Invalid party information', {
                        errors
                    });
                }

                const validationErrors = validatePartyInput(input);
                if (validationErrors.length > 0) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Invalid party information', {
                        errors: validationErrors
                    });
                }

                const { coHosts = [], ...partyInput } = input;

                if (coHosts.length > 0) {
                    const result = await processCoHosts(coHosts);
                    if (result.errors) {
                        return createResponse('PartyErrorResponse', 'FAILURE', 'Invalid co-host information', {
                            errors: result.errors
                        });
                    }
                    partyInput.coHosts = result.coHostIds;
                }

                const party = await new Party({
                    ...partyInput,
                    coHosts: partyInput.coHosts
                }).save();

                const invitation = new Invitation({
                    party: party._id,
                    message: "You are invited to the party",
                    media: []
                });

                await invitation.save();

                const invitationSettings = new InvitationSettings({
                    party: party._id,
                    is_guest_list_public: false,
                    additional_guest_allowed: false,
                    additional_guest_limit: 0,
                    allow_guest_to_add_photo: false,
                    send_auto_reminder_to_all_guests: false
                });

                await invitationSettings.save();

                party.invitationSettings = invitationSettings._id;
                party.invitation = invitation._id;
                party.rsvps = [];
                party.guests = [];
                await party.save();

                const taskResult = await createTasksFromChecklist(
                    party._id,
                    party.partyType,
                    party.eventId
                );

                if (!taskResult.success) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Error creating party tasks', {
                        errors: [taskResult.error]
                    });
                }

                if (input.time) {
                    await scheduleParty(party);
                }

                return createResponse('PartyResponse', 'SUCCESS', 'Party created successfully', {
                    result: { party }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyErrorResponse', 'FAILURE', 'Error creating party', {
                    errors: [{
                        field: 'party',
                        message: error.message || 'An unexpected error occurred'
                    }]
                });
            }
        },

        updateParty: async (_, { id, input }, context) => {
            try {
                const eventId = input.eventId || (await Party.findById(id))?.eventId;
                if (!await hasEventPartyLevelAccess(context.user._id, eventId, id)) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                    });
                }

                const { coHosts = [], ...partyInput } = input;

                const validationError = await validateReferences(partyInput, partyIdSchema, 'Party');
                if (validationError) {
                    return createResponse('PartyErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const existingParty = await Party.findById(id);
                if (!existingParty) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                if (partyInput.partyType && partyInput.partyType.toString() !== existingParty.partyType.toString()) {
                    const createResult = await createTasksFromChecklist(
                        id,
                        partyInput.partyType,
                        existingParty.eventId
                    );

                    if (!createResult.success) {
                        return createResponse('PartyErrorResponse', 'FAILURE', 'Error creating new party tasks', {
                            errors: [createResult.error]
                        });
                    }
                }

                if (input.hasOwnProperty('coHosts')) {
                    const result = await processCoHosts(coHosts);
                    if (result.errors) {
                        return createResponse('PartyErrorResponse', 'FAILURE', 'Invalid co-host information', {
                            errors: result.errors
                        });
                    }
                    partyInput.coHosts = result.coHostIds || [];
                    
                    // Find new cohosts by comparing with existing ones
                    const existingCoHosts = existingParty.coHosts || [];
                    const newCoHosts = partyInput.coHosts.filter(
                        coHostId => !existingCoHosts.includes(coHostId)
                    );
                    
                    if (newCoHosts.length > 0) {
                        await notifyCoHostAdded(existingParty, newCoHosts, context.user._id);
                    }
                }

                if (partyInput.invitation) {
                    const invitation = await Invitation.findOneAndUpdate(
                        { party: id }, 
                        { 
                            message: partyInput.invitation.message || "You are invited to the party", 
                            media: partyInput.invitation.media || [] 
                        }, 
                        { new: true }
                    );

                    if (!invitation) {
                        const newInvitation = new Invitation({
                            party: id,
                            message: partyInput.invitation.message || "You are invited to the party",
                            media: partyInput.invitation.media || []
                        });
                        await newInvitation.save();
                    }
                }

                if (partyInput.invitationSettings) {
                    const invitationSettings = await InvitationSettings.findOneAndUpdate(
                        { party: id },
                        {
                            is_guest_list_public: partyInput.invitationSettings.is_guest_list_public ?? false,
                            additional_guest_allowed: partyInput.invitationSettings.additional_guest_allowed ?? false,
                            additional_guest_limit: partyInput.invitationSettings.additional_guest_limit ?? 0,
                            allow_guest_to_add_photo: partyInput.invitationSettings.allow_guest_to_add_photo ?? false,
                            send_auto_reminder_to_all_guests: partyInput.invitationSettings.send_auto_reminder_to_all_guests ?? false
                        },
                        { new: true, upsert: true }
                    );

                    partyInput.invitationSettings = invitationSettings._id;
                }

                const updatedParty = await Party.findByIdAndUpdate(
                    id,
                    partyInput,
                    { new: true }
                );

                await updateMediaFolderOwners(
                    id,
                    'party',
                    existingParty.eventId,
                    partyInput.coHosts || existingParty.coHosts
                );

                await notifyPartyUpdate(existingParty, updatedParty, context.user._id);
                await clearCacheById(id);

                if (input.time) {
                    if (existingParty.time?.toString() !== input.time.toString()) {
                        await deleteScheduledParty(existingParty);
                    }
                    await scheduleParty(updatedParty);
                }

                return createResponse('PartyResponse', 'SUCCESS', 'Party updated successfully', {
                    result: { party: updatedParty }
                });

            } catch (error) {
                console.error(error);
                return createResponse('PartyErrorResponse', 'FAILURE', 'Error updating party', {
                    errors: [{ field: 'updateParty', message: error.message }]
                });
            }
        },

        deleteParty: async (_, { id }, context) => {
            try {
                const partyFetched = await Party.findById(id);
                const eventId = partyFetched.eventId;
                if (!await hasEventPartyLevelAccess(context.user._id, eventId, id)) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                    });
                }

                const references = await findReferences(id, 'Party');
                if (references.length > 0) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Party cannot be deleted', {
                        errors: [{ field: 'id', message: `Party cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                await notifyPartyCancellation(partyFetched, context.user._id);

                const result = await cascadeDelete('Party', id);
                if (!result.success) {
                    return createResponse('PartyErrorResponse', 'FAILURE', 'Error deleting party', {
                        errors: [{ field: 'deleteParty', message: result.error.message }]
                    });
                }

                await clearCacheById(id);
                return createResponse('PartyResponse', 'SUCCESS', 'Party deleted successfully', {
                    result: { party: partyFetched }
                });
            } catch (error) {
                console.error(error);
                return createResponse('PartyErrorResponse', 'FAILURE', 'Error deleting party', {
                    errors: [{ field: 'deleteParty', message: error.message }]
                });
            }
        }
    }
};

module.exports = partyResolvers; 