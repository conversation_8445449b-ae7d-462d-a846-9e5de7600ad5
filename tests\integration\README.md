# Integration Testing Documentation

This directory contains comprehensive integration tests for the GraphQL API project. These tests verify that all components work together correctly, including the GraphQL API, REST endpoints, database operations, and external service integrations.

## Test Structure

```
tests/integration/
├── setup.js                           # Integration test setup and configuration
├── utils/
│   └── testHelpers.js                 # Test utilities and helper functions
├── graphql/
│   ├── user.integration.test.js       # User operations tests
│   ├── event.integration.test.js      # Event management tests
│   ├── party.integration.test.js      # Party management tests
│   ├── task.integration.test.js       # Task management tests
│   └── article.integration.test.js    # Article operations tests
├── rest/
│   ├── webhook.integration.test.js    # Webhook endpoint tests
│   ├── events.integration.test.js     # SSE events endpoint tests
│   └── notifications.integration.test.js # SSE notifications endpoint tests
└── auth/
    └── authentication.integration.test.js # Authentication & authorization tests
```

## Test Coverage

### GraphQL API Tests
- **User Operations**: Registration, authentication, profile management, role updates
- **Event Management**: CRUD operations, filtering, pagination, user access control
- **Party Management**: Party creation, updates, guest management, venue handling
- **Task Management**: Task assignment, comments, attachments, status tracking
- **Article Operations**: External API integration, content management, filtering

### REST API Tests
- **Webhook Endpoints**: User creation webhooks, signature verification, error handling
- **SSE Events**: Real-time event streaming, authentication, connection management
- **SSE Notifications**: User-specific notifications, connection isolation, CORS handling

### Authentication & Authorization Tests
- **Token Validation**: JWT verification, expiration handling, signature validation
- **Role-Based Access**: Admin vs user permissions, resource access control
- **Clerk Integration**: External authentication service integration
- **Security**: Malformed requests, unauthorized access attempts

## Test Environment

### Database
- Uses MongoDB Memory Server for isolated test database
- Each test gets a clean database state
- No external database dependencies

### External Services
- Clerk authentication service is mocked
- Redis cache is mocked
- AWS services (S3, CloudWatch) are mocked
- External APIs are mocked with controlled responses

### Configuration
- Test-specific environment variables in `.env.test`
- Isolated from development/production configurations
- Mock credentials and endpoints

## Running Tests

### All Integration Tests
```bash
npm run test:integration
```

### Watch Mode (for development)
```bash
npm run test:integration:watch
```

### Specific Test Files
```bash
# User tests only
npx jest tests/integration/graphql/user.integration.test.js

# Authentication tests only
npx jest tests/integration/auth/authentication.integration.test.js

# REST API tests only
npx jest tests/integration/rest/
```

### With Coverage
```bash
npm run test:coverage
```

## Test Utilities

### GraphQLTestHelper
Provides convenient methods for testing GraphQL operations:
```javascript
const helper = new GraphQLTestHelper(app);
helper.setAuthToken('Bearer token');
const response = await helper.query(query, variables);
```

### TestDataFactory
Creates test data with realistic relationships:
```javascript
const user = await TestDataFactory.createUser();
const event = await TestDataFactory.createEvent(user);
const party = await TestDataFactory.createParty(event);
```

### AuthTestUtils
Manages authentication mocking:
```javascript
AuthTestUtils.mockClerkVerification(userId, roles);
const token = AuthTestUtils.createMockToken(userId);
```

### DatabaseTestUtils
Database management utilities:
```javascript
await DatabaseTestUtils.clearAllCollections();
const count = await DatabaseTestUtils.countDocuments(Model);
```

## Test Patterns

### Setup and Teardown
- Global setup creates test server and database connection
- Each test gets a clean database state
- Mocks are reset between tests
- Connections are properly closed after all tests

### Authentication Testing
- Mock Clerk service responses
- Test both valid and invalid tokens
- Verify role-based access control
- Test edge cases (expired tokens, malformed headers)

### Error Handling
- Test invalid input validation
- Test database connection failures
- Test external service unavailability
- Test malformed requests

### Real-time Features
- Test SSE connection establishment
- Test message broadcasting
- Test connection cleanup
- Test authentication for streaming endpoints

## Best Practices

### Test Isolation
- Each test is independent
- No shared state between tests
- Clean database before each test
- Reset all mocks between tests

### Realistic Data
- Use factory functions for test data
- Maintain referential integrity
- Test with realistic data volumes
- Test edge cases and boundary conditions

### Comprehensive Coverage
- Test happy paths and error cases
- Test authentication and authorization
- Test input validation
- Test external service integration

### Performance Considerations
- Tests run in parallel where possible
- Use in-memory database for speed
- Mock external services to avoid network delays
- Reasonable timeouts for async operations

## Debugging Tests

### Verbose Output
```bash
npx jest tests/integration/ --verbose
```

### Debug Specific Test
```bash
npx jest tests/integration/graphql/user.integration.test.js --verbose --no-cache
```

### Database Inspection
Tests use MongoDB Memory Server, so you can inspect the database state during debugging by adding console.log statements or using a debugger.

### Mock Inspection
All mocks are available for inspection:
```javascript
expect(mockedFunction).toHaveBeenCalledWith(expectedArgs);
expect(mockedFunction).toHaveBeenCalledTimes(expectedCount);
```

## Continuous Integration

These tests are designed to run in CI/CD environments:
- No external dependencies
- Deterministic results
- Proper cleanup
- Clear error messages
- Reasonable execution time

## Extending Tests

When adding new features:
1. Add corresponding integration tests
2. Update test data factories if needed
3. Mock any new external services
4. Test both success and failure scenarios
5. Update this documentation
