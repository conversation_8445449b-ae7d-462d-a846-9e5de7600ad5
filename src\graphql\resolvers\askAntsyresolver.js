const { createResponse } = require("../../utils/response.util");
const { addMessageToActivityQueue } = require("../../utils/messageQueue.util");
const Message = require("../../models/Message");
const { hasEventPartyGuestLevelAccess } = require("../../utils/auth/accessLevels.util");
const Party = require("../../models/Party");

const { clearCacheById } = require('../../utils/cache.util');

const createMessage = async (answer, party, context ) => {
    const antsyResponse = new Message({
        type: 'SYSTEM',
        text: answer,
        receivers: [context.user._id],
    });

    await antsyResponse.save();
    party.activity.push(antsyResponse._id);
    await party.save();
    clearCacheById(party._id);

    await addMessageToActivityQueue(party._id, { eventId: party.eventId, message: antsyResponse, sub_type: 'MESSAGE_CREATED' });

    return createResponse('MessageResponse', 'SUCCESS', 'Message created successfully', {
        result: { message: antsyResponse }
    });
}

const askAntsyResolver = {
    Query: {
        traffic: async (_, { input }, context) => {
            const { lat, lng, partyId } = input;

            const party = await Party.findById(partyId);

            if (!party) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                    errors: [{ field: 'partyId', message: 'Party not found' }]
                });
            }

            const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, party._id);
            if (!hasAccess) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                    errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                });
            }
            const answer = "Traffic data fetched successfully";
            return await createMessage(answer, party, context);
        },
        weather: async (_, { input }, context) => {
            const { partyId } = input;
            const party = await Party.findById(partyId);

            if (!party) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                    errors: [{ field: 'partyId', message: 'Party not found' }]
                });
            }

            const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, partyId);
            if (!hasAccess) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                    errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                });
            }
            const answer = "Weather data fetched successfully";
            return await createMessage(answer, party, context);
        },
        venueDetails: async (_, { input }, context) => {
            const { partyId } = input;
            const party = await Party.findById(partyId);

            if (!party) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                    errors: [{ field: 'partyId', message: 'Party not found' }]
                });
            }

            const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, partyId);
            if (!hasAccess) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                    errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                });
            }
            const answer = "Venue details fetched successfully";
            return await createMessage(answer, party, context);
        },
        partyDetails: async (_, { input }, context) => {
            const { partyId } = input;
            const party = await Party.findById(partyId);

            if (!party) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Party not found', {
                    errors: [{ field: 'partyId', message: 'Party not found' }]
                });
            }

            const { hasAccess } = await hasEventPartyGuestLevelAccess(context.user._id, party.eventId, partyId);
            if (!hasAccess) {
                return createResponse('MessageErrorResponse', 'FAILURE', 'Unauthorized access', {
                    errors: [{ field: 'authorization', message: 'You do not have access to this party' }]
                });
            }
            const answer = "Party details fetched successfully";
            return await createMessage(answer, party, context);
        },
    },
};

module.exports = askAntsyResolver;