// Global test setup for all tests
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGODB = process.env.MONGODB_TEST || 'mongodb://localhost:27017/graphql-api-test';
process.env.REDIS_URL = process.env.REDIS_URL_TEST || 'redis://localhost:6379/1';

// Increase timeout for all tests
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};
