const { gql } = require('graphql-tag');
const sharedTypeDef = require('./shared.typedef');

module.exports = gql`
    ${sharedTypeDef}

    enum ReceiverType {
        Host
        Cohost
        Collaborators
        Guest
        Organizer
        Member
    }

    type NavigationConfig {
        route: String!
        params: [String!]
        queryParams: [String!]
    }

    type NotificationTemplate {
        subject: String!
        message: String!
        navigation: NavigationConfig
    }

    type NotificationChannels {
        activity: Boolean!
        sms: Boolean!
        whatsapp: Boolean!
        inApp: Boolean!
        push: Boolean!
        email: Boolean!
    }

    type NotificationEvent {
        receivers: [ReceiverType!]!
        channels: NotificationChannels!
        templates: NotificationTemplate!
        requiredData: [String!]!
    }

    type MdNotificationConfig {
        id: ID!
        eventCategory: String!
        eventType: String!
        config: NotificationEvent!
    }

    input NavigationConfigInput {
        route: String!
        params: [String!]
        queryParams: [String!]
    }

    input NotificationTemplateInput {
        subject: String!
        message: String!
        navigation: NavigationConfigInput
    }

    input NotificationChannelsInput {
        activity: Boolean!
        sms: Boolean!
        whatsapp: Boolean!
        inApp: Boolean!
        push: Boolean!
        email: Boolean!
    }

    input NotificationEventInput {
        receivers: [ReceiverType!]!
        channels: NotificationChannelsInput!
        templates: NotificationTemplateInput!
        requiredData: [String!]
    }

    input MdNotificationConfigInput {
        eventCategory: String!
        eventType: String!
        config: NotificationEventInput!
    }

    type MdNotificationConfigWrapper {
        notificationConfig: MdNotificationConfig!
    }

    type MdNotificationConfigsWrapper {
        notificationConfigs: [MdNotificationConfig]!
    }

    type MdNotificationConfigResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdNotificationConfigWrapper!
    }

    type MdNotificationConfigsResponse implements Response {
        status: ResponseStatus!
        message: String!
        result: MdNotificationConfigsWrapper!
    }

    type MdNotificationConfigErrorResponse implements Response {
        status: ResponseStatus!
        message: String!
        errors: [Error!]!
    }

    union MdNotificationConfigResult = MdNotificationConfigResponse | MdNotificationConfigErrorResponse
    union MdNotificationConfigsResult = MdNotificationConfigsResponse | MdNotificationConfigErrorResponse

    type Query {
        getMdNotificationConfig(id: ID!): MdNotificationConfigResult!
        getMdNotificationConfigs: MdNotificationConfigsResult!
    }

    type Mutation {
        createMdNotificationConfig(input: MdNotificationConfigInput!): MdNotificationConfigResult!
        updateMdNotificationConfig(id: ID!, input: MdNotificationConfigInput!): MdNotificationConfigResult!
        deleteMdNotificationConfig(id: ID!): MdNotificationConfigResult!
    }
`;