const Event = require('../../models/Event');
const Host = require('../../models/Host');
const Guest = require('../../models/Guest');
const MdEventType = require('../../models/MdEventType');
const MdServiceLocation = require('../../models/MdServiceLocation');
const Party = require('../../models/Party');
const PartyService = require('../../models/PartyService');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const buildEventQuery = require('./filters/event.filter');
const { findReferences } = require('../../utils/referenceCheck.util');
const { validateReferences } = require('../../utils/validation.util');
const { processCoHosts } = require('../../utils/coHost.util');
const mongoose = require('mongoose');
const Task = require('../../models/Task');
const buildEventTaskQuery = require('./filters/eventTask.filter');
const { User } = require('../../models/User');
const MediaFolder = require('../../models/MediaFolder');
const { deleteMediaAndRelatedData, updateMediaFolderOwners } = require('../../utils/media.util');
const { hasEventLevelAccess, hasGuestLevelAccess } = require('../../utils/auth/accessLevels.util');
const TaskCollaborator = require('../../models/TaskCollaborator');
const paginationModifier = require('../../utils/paginationInfo.util');
const EventGroup = require('../../models/EventGroup');
const { notifyNewEventAdded } = require('../../notification/circleNotification');
const { cascadeDelete } = require('../../utils/cascadeDelete.util');

const baseEventIdSchema = {
    eventType: { type: 'single', model: MdEventType },
    mainHost: { type: 'single', model: Host },  
    coHosts: { type: 'array', required: false, model: Host },
    guests: { type: 'array', required: false, model: Guest },
    location: { type: 'single', model: MdServiceLocation },
    eventGroupId: { type: 'single', model: EventGroup }
};

const eventIdSchema = { ...baseEventIdSchema };

const createEventIdSchema = {
    ...baseEventIdSchema,
    mainHost: { ...baseEventIdSchema.mainHost, required: true },
    location: { ...baseEventIdSchema.location, required: false }
};

const getEventUsers = async (event, includeGuests = false) => {
    const userIds = new Set();

    if (event.mainHost) {
        const host = await Host.findById(event.mainHost);
        if (host?.userId) userIds.add(host.userId.toString());
    }

    const parties = await Party.find({ eventId: event._id });
    const allCoHostIds = [
        ...(event.coHosts || []),
        ...parties.reduce((ids, party) => [...ids, ...(party.coHosts || [])], [])
    ];

    if (allCoHostIds.length) {
        const coHosts = await Host.find({ _id: { $in: allCoHostIds } });
        coHosts.forEach(host => host.userId && userIds.add(host.userId.toString()));
    }

    if (includeGuests && parties.length) {
        const guests = await Guest.find({ party: { $in: parties.map(p => p._id) } });
        guests.forEach(guest => guest.user && userIds.add(guest.user.toString()));
    }

    return User.find({ _id: { $in: Array.from(userIds) } });
};

const eventResolvers = {
    Event: {
        eventType: async (parent) => {
            try {
                return await MdEventType.findById(parent.eventType);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event type');
            }
        },
        mainHost: async (parent) => {
            try {
                return await Host.findById(parent.mainHost);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting main host');
            }
        },
        coHosts: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id });
                const partyCoHostIds = parties.reduce((ids, party) => {
                    if (party.coHosts && party.coHosts.length > 0) {
                        return [...ids, ...party.coHosts];
                    }
                    return ids;
                }, []);

                const uniqueCoHostIds = [...new Set([
                    ...(parent.coHosts || []),
                    ...partyCoHostIds
                ])];

                const hosts = await Host.find({ _id: { $in: uniqueCoHostIds } });

                return hosts;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting co-hosts');
            }
        },
        guests: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id });
                const partyIds = parties.map(party => party._id);
                return await Guest.find({ party: { $in: partyIds } });
            } catch (error) {
                console.error(error);
                throw new Error('Error getting guests');
            }
        },
        budget: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id });
                let totalBudget = 0;

                for (const party of parties) {
                    const partyServices = await PartyService.find({ _id: { $in: party.services } });
                    totalBudget += partyServices.reduce((total, service) => total + (service.budget || 0), 0);
                }

                return totalBudget;
            } catch (error) {
                console.error(error);
                throw new Error('Error calculating total budget');
            }
        },
        location: async (parent) => {
            try {
                if (!parent.location) return null;
                return await MdServiceLocation.findById(parent.location);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting location');
            }
        },
        parties: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id });
                return parties;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting parties');
            }
        },
        startDate: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id })
                    .select('time')
                    .sort({ time: 1 })
                    .limit(1)
                    .lean();

                if (parties[0]?.time) {
                    return parties[0].time;
                }

                const event = await Event.findById(parent._id)
                    .select('startDate')
                    .lean();

                return event?.startDate || null;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting start date');
            }
        },
        endDate: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id })
                    .select('time')
                    .sort({ time: -1 })
                    .limit(1)
                    .lean();

                if (parties[0]?.time) {
                    return parties[0].time;
                }

                const event = await Event.findById(parent._id)
                    .select('endDate')
                    .lean();

                return event?.endDate || null;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting end date');
            }
        },
        tasks: async (parent) => {
            try {
                const parties = await Party.find({ eventId: parent._id });
                const partyIds = parties.map(party => party._id);
                const tasks = await Task.find({ party: { $in: partyIds } });
                
                return tasks;
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event tasks');
            }
        },
        eventGroup: async (parent) => {
            try {
                return await EventGroup.findById(parent.eventGroupId);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting event group');
            }
        }
    },

    Query: {
        getEventById: async (_, { id }, context) => {
            try {
                // Check if user has access to this event
                const hasAccess = await hasGuestLevelAccess(context.user._id, id);
                if (!hasAccess) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                let event = await getByIdCache(id);
                if (!event) {
                    event = await Event.findById(id);
                    if (!event) {
                        return createResponse('EventErrorResponse', 'FAILURE', 'Event not found', {
                            errors: [{ field: 'id', message: 'Event not found' }]
                        });
                    }
                    await setCache(id, event);
                }

                return createResponse('EventResponse', 'SUCCESS', 'Event retrieved successfully', {
                    result: { event }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error retrieving event', {
                    errors: [{ field: 'getEventById', message: error.message }]
                });
            }
        },

        getEvents: async (_, { filter, pagination }, context) => {
            try {
                const { query: baseQuery, applyPostQueryFilters } = await buildEventQuery(filter || {}, context.user._id);
                const userId = context.user._id;

                // Get user's host record
                const userHost = await Host.findOne({ userId });
                const userHostIds = userHost ? [userHost._id] : [];
                
                // Get all parties where user is a guest
                const userGuests = await Guest.find({ user: userId });
                const userParties = await Party.find({ 
                    _id: { $in: userGuests.map(g => g.party) }
                });
                
                // Get event IDs where user is a party co-host
                const partyCoHostEventIds = userHost ? 
                    (await Party.find({ coHosts: userHost._id }))
                        .map(p => p.eventId)
                    : [];

                // Combine access conditions with the base query
                const query = {
                    ...baseQuery,
                    $or: [
                        // User is main host or co-host of the event
                        ...(userHost ? [
                            { mainHost: userHost._id },
                            { coHosts: userHost._id }
                        ] : []),
                        // User is a guest in any party of the event
                        ...(userParties.length > 0 ? [
                            { _id: { $in: userParties.map(p => p.eventId) } }
                        ] : []),
                        // User is a co-host in any party of the event
                        ...(partyCoHostEventIds.length > 0 ? [
                            { _id: { $in: partyCoHostEventIds } }
                        ] : [])
                    ]
                };

                // If user has no access roles, return empty result
                if (!query.$or?.length) {
                    return createResponse('EventsResponse', 'SUCCESS', 'No events found', {
                        result: { events: [] },
                        pagination: {
                            totalItems: 0,
                            totalPages: 0,
                            currentPage: 1,
                            pageSize: pagination?.limit || 10,
                            skip: pagination?.skip || 0
                        }
                    });
                }

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                let events = await Event.find(query)
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                events = await applyPostQueryFilters(events);
                
                events = events.map(event => {
                    const eventObj = event.toObject ? event.toObject() : event;
                    return {
                        ...eventObj,
                        id: eventObj._id.toString()
                    };
                });

                const paginationInfo = await getPaginationInfo(Event, query, limit, skip);

                if (events.length === 0) {
                    return createResponse('EventsResponse', 'FAILURE', 'No events found', {
                        result: { events },
                        pagination: paginationInfo
                    });
                }

                return createResponse('EventsResponse', 'SUCCESS', 'Events fetched successfully', {
                    result: { events },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error retrieving events', {
                    errors: [{ field: 'getEvents', message: error.message }]
                });
            }
        },

        getUserEvents: async (_, { filter, pagination }, context) => {
            try {
                const userId = context.user._id;
                const { query: baseQuery, applyPostQueryFilters } = await buildEventQuery(filter || {}, context.user._id);

                let events = await Event.find(baseQuery)
                    .select('_id name description mainHost coHosts createdAt')
                    .sort({ createdAt: -1 })
                    .lean();

                events = await applyPostQueryFilters(events);

                const [userHost, userGuests] = await Promise.all([
                    Host.findOne({ userId }).select('_id').lean(),
                    Guest.find({ user: userId }).select('party').lean()
                ]);

                const partyIds = userGuests.map(g => g.party);
                const [userParties, partyCoHosts] = await Promise.all([
                    Party.find({ 
                        _id: { $in: partyIds },
                        eventId: { $in: events.map(e => e._id) }
                    }).select('eventId').lean(),
                    userHost ? Party.find({ 
                        coHosts: userHost._id,
                        eventId: { $in: events.map(e => e._id) }
                    }).select('eventId').lean() : []
                ]);

                const partyEventMap = new Map(userParties.map(p => [p.eventId.toString(), true]));
                const coHostEventMap = new Map(partyCoHosts.map(p => [p.eventId.toString(), true]));

                events = events.map(event => {
                    const status = new Set();
                    const eventId = event._id.toString();

                    if (userHost) {
                        if (event.mainHost.toString() === userHost._id.toString()) {
                            status.add('MAIN_HOST');
                        }
                        if (event.coHosts?.some(id => id.toString() === userHost._id.toString())) {
                            status.add('CO_HOST');
                        }
                    }

                    if (partyEventMap.has(eventId)) {
                        status.add('GUEST');
                    }
                    if (coHostEventMap.has(eventId)) {
                        status.add('CO_HOST');
                    }

                    return {
                        ...event,
                        id: eventId,
                        status: Array.from(status)
                    };
                });

                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;
                const totalItems = events.length;
                const totalPages = Math.ceil(totalItems / limit);
                const currentPage = Math.floor(skip / limit) + 1;

                const paginatedEvents = events.slice(skip, skip + limit);

                const paginationInfo = {
                    totalItems,
                    totalPages,
                    currentPage,
                    pageSize: limit
                };

                if (paginatedEvents.length === 0) {
                    return createResponse('EventsResponse', 'SUCCESS', 'No events found', {
                        result: { events: [] },
                        pagination: paginationInfo
                    });
                }

                return createResponse('EventsResponse', 'SUCCESS', 'Events retrieved successfully', {
                    result: { events: paginatedEvents },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error('Error in getUserEvents:', error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error retrieving events', {
                    errors: [{ field: 'getUserEvents', message: error.message }]
                });
            }
        },

        getEventTasks: async (_, { filter, eventId }, context) => {
            try {
                const hasEventAccess = await hasEventLevelAccess(context.user._id, eventId);
                
                const parties = await Party.find({ eventId });
                
                if (!parties.length) {
                    return createResponse('PartyTasksResponse', 'SUCCESS', 'No tasks found', {
                        result: { 
                            partyTasks: [],
                            totalTasksCount: 0,
                            totalFilteredTasksCount: 0
                        }
                    });
                }

                const partyIds = parties.map(party => party._id);
                
                let baseQuery = { party: { $in: partyIds } };
                if (!hasEventAccess) {
                    const collaboratorTasks = await TaskCollaborator.find({ 
                        user: context.user._id 
                    }).select('task');
                    
                    const collaboratorTaskIds = collaboratorTasks.map(ct => ct.task);
                    
                    if (collaboratorTaskIds.length === 0) {
                        return createResponse('EventErrorResponse', 'FAILURE', 'Unauthorized access', {
                            errors: [{ field: 'authorization', message: 'You do not have access to these tasks' }]
                        });
                    }
                    
                    baseQuery = {
                        ...baseQuery,
                        _id: { $in: collaboratorTaskIds }
                    };
                }

                const totalTasksCount = await Task.countDocuments(baseQuery);

                const filterQuery = filter ? await buildEventTaskQuery(filter) : {};
                const query = { ...baseQuery, ...filterQuery };

                if (filter?.partyId) {
                    query.party = filter.partyId;
                }

                const tasks = await Task.find(query)
                    .populate('attachments')
                    .populate('comments')
                    .populate('createdBy')
                    .populate('assignedTo')
                    .sort({ createdAt: -1 });

                const tasksByParty = new Map();
                for (const task of tasks) {
                    const partyId = task.party.toString();
                    if (!tasksByParty.has(partyId)) {
                        tasksByParty.set(partyId, []);
                    }
                    tasksByParty.get(partyId).push(task);
                }

                const partyTasks = await Promise.all(
                    parties
                        .filter(party => !filter?.partyId || party._id.toString() === filter.partyId)
                        .map(async (party) => {
                            const partyTasks = tasksByParty.get(party._id.toString()) || [];
                            return {
                                party,
                                tasks: partyTasks,
                                tasksCount: partyTasks.length
                            };
                        })
                );

                return createResponse('PartyTasksResponse', 'SUCCESS', 'Tasks fetched successfully', {
                    result: { 
                        partyTasks,
                        totalTasksCount,
                        totalFilteredTasksCount: tasks.length
                    }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error retrieving tasks', {
                    errors: [{ field: 'getEventTasks', message: error.message }]
                });
            }
        },

        getEventCollaborators: async (_, { eventId }, context) => {
            try {
                const event = await Event.findById(eventId);
                if (!event) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Event not found', {
                        errors: [{ field: 'eventId', message: 'Event not found' }]
                    });
                }

                const hasAccess = await hasEventLevelAccess(context.user._id, eventId);
                if (!hasAccess) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const collaborators = await getEventUsers(event, true);
                return createResponse('EventCollaboratorsResponse', 'SUCCESS', 'Event collaborators fetched successfully', {
                    result: { collaborators }
                });
            } catch (error) {
                console.error('Error in getEventCollaborators:', error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error fetching event collaborators', {
                    errors: [{ field: 'getEventCollaborators', message: error.message }]
                });
            }
        },

        getEventAssignees: async (_, { eventId }, context) => {
            try {
                const event = await Event.findById(eventId);
                if (!event) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Event not found', {
                        errors: [{ field: 'eventId', message: 'Event not found' }]
                    });
                }

                const hasAccess = await hasEventLevelAccess(context.user._id, eventId);
                if (!hasAccess) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Unauthorized access', {
                        errors: [{ field: 'authorization', message: 'You do not have access to this event' }]
                    });
                }

                const assignees = await getEventUsers(event, false);
                return createResponse('EventAssigneesResponse', 'SUCCESS', 'Event assignees fetched successfully', {
                    result: { assignees }
                });
            } catch (error) {
                console.error('Error in getEventAssignees:', error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error fetching event assignees', {
                    errors: [{ field: 'getEventAssignees', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createEvent: async (_, { input }, context) => {
            try {
                const { coHosts = [], ...eventInput } = input;
                let mainHost = await Host.findOne({ userId: context.user._id.toString() });
                if(!mainHost) {
                    mainHost = new Host({ userId: context.user._id.toString() });
                    await mainHost.save();
                }
                eventInput.mainHost = mainHost._id;
                const validationError = await validateReferences(eventInput, createEventIdSchema, 'Event');
                if (validationError) {
                    return createResponse('EventErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                if (coHosts.length > 0) {
                    const result = await processCoHosts(coHosts);
                    if (result.errors) {
                        return createResponse('EventErrorResponse', 'FAILURE', 'Invalid co-host information', {
                            errors: result.errors
                        });
                    }
                    eventInput.coHosts = result.coHostIds;
                }

                const event = new Event(eventInput);
                await event.save();

                const ownerIds = await updateMediaFolderOwners(
                    event._id,
                    'event',
                    eventInput.mainHost,
                    eventInput.coHosts
                );

                const mediaFolder = new MediaFolder({
                    name: eventInput.name,
                    event: event._id,
                    owner: ownerIds,
                    publish: false,
                    category: 'ALBUM',
                });

                await mediaFolder.save();

                if (eventInput.eventGroupId) 
                    await notifyNewEventAdded(eventInput.eventGroupId, event, context.user._id);
                
                return createResponse('EventResponse', 'SUCCESS', 'Event created successfully', {
                    result: { event }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error creating event', {
                    errors: [{ field: 'createEvent', message: error.message }]
                });
            }
        },

        updateEvent: async (_, { id, input }) => {
            try {
                const { coHosts = [], ...eventInput } = input;

                const validationError = await validateReferences(eventInput, eventIdSchema, 'Event');
                if (validationError) {
                    return createResponse('EventErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

                const existingEvent = await Event.findById(id);
                if (!existingEvent) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Event not found', {
                        errors: [{ field: 'id', message: 'Event not found' }]
                    });
                }

                if (input.hasOwnProperty('coHosts')) {
                    const result = await processCoHosts(coHosts);
                    if (result.errors) {
                        return createResponse('EventErrorResponse', 'FAILURE', 'Invalid co-host information', {
                            errors: result.errors
                        });
                    }
                    eventInput.coHosts = result.coHostIds || [];
                }

                const event = await Event.findByIdAndUpdate(
                    id,
                    eventInput,
                    { new: true }
                );

                await updateMediaFolderOwners(
                    id,
                    'event',
                    eventInput.mainHost || existingEvent.mainHost,
                    eventInput.coHosts || existingEvent.coHosts
                );

                await clearCacheById(id);

                if (eventInput.eventGroupId) 
                    await notifyNewEventAdded(eventInput.eventGroupId, event, context.user._id);

                return createResponse('EventResponse', 'SUCCESS', 'Event updated successfully', {
                    result: { event }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error updating event', {
                    errors: [{ field: 'updateEvent', message: error.message }]
                });
            }
        },

        deleteEvent: async (_, { id }) => {
            try {
                const references = await findReferences(id, 'Event');
                if (references.length > 0) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Event cannot be deleted', {
                        errors: [{ field: 'deleteEvent', message: `Event cannot be deleted as it is being used in: ${references.join(', ')}` }]
                    });
                }

                const event = await Event.findById(id);
                if (!event) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Event not found', {
                        errors: [{ field: 'id', message: 'Event not found' }]
                    });
                }

                const result = await cascadeDelete('Event', id);
                if (!result.success) {
                    return createResponse('EventErrorResponse', 'FAILURE', 'Error deleting event', {
                        errors: [{ field: 'deleteEvent', message: result.error.message }]
                    });
                }

                return createResponse('EventResponse', 'SUCCESS', 'Event and all related data deleted successfully', {
                    result: { event }
                });
            } catch (error) {
                console.error(error);
                return createResponse('EventErrorResponse', 'FAILURE', 'Error deleting event', {
                    errors: [{ field: 'deleteEvent', message: error.message }]
                });
            }
        }
    }
};
module.exports = eventResolvers;
