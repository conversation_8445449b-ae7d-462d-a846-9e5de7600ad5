const Guest = require('../../models/Guest');
const { User } = require('../../models/User');
const { createResponse } = require('../../utils/response.util');
const { getPaginationInfo } = require('../../utils/paginationInfo.util');
const { getByIdCache, setCache, clearCacheById } = require('../../utils/cache.util');
const { validateReferences } = require('../../utils/validation.util');
const Party = require('../../models/Party');
const Invitation = require('../../models/Invitation');
const InvitationRSVP = require('../../models/InvitationRSVP');
const PhoneNumberScalar = require('../resolvers/scalars/phoneNumber');
const buildGuestQuery = require('./filters/guest.filter');
const LiveLocation = require('../../models/liveLocation');
const Event = require('../../models/Event');
const TaskCollaborator = require('../../models/TaskCollaborator');
const { deleteMediaAndRelatedData } = require('../../utils/media.util');
const MediaFolder = require('../../models/MediaFolder');

const guestIdSchema = {
    userId: { type: 'single', required: true, model: User },
    partyId: { type: 'single', required: true, model: Party }
};

const createGuestAndInvitation = async (userId, partyId) => {
    const guest = new Guest({ user: userId, party: partyId });
    await guest.save();

    const party = await Party.findById(partyId).populate('eventId');
    if (!party) {
        throw new Error('Party not found');
    }

    const invitation = await Invitation.findOne({ party: party._id });
    if (!invitation) {
        const newInvitation = new Invitation({
            party: party._id,
            status: 'PENDING'
        });
        await newInvitation.save();
        invitation = newInvitation;
    }

    if (!party.guests) {
        party.guests = [];
    }
    if (!party.rsvps) {
        party.rsvps = [];
    }

    party.guests.push(guest._id);
    await party.save();

    const mediaFolder = await MediaFolder.findOne({ event: party.eventId._id });
    if (!mediaFolder) {
        throw new Error('Media folder not found');
    }

    mediaFolder.contributors.push(userId);
    await mediaFolder.save();

    return { guest, party };
};


const findOrCreateUser = async (contact) => {
    let user = await User.findOne({ phone: contact.phone });
    if (!user) {
        user = new User({
            firstName: contact.firstName,
            lastName: contact.lastName,
            email: contact.email,
            phone: contact.phone
        });
        await user.save();
    }
    return user;
};

const deleteGuestRsvpAndLocation = async (guestId) => {
    try {
        const existingRsvp = await InvitationRSVP.findOne({ guest: guestId });
        const existingLocation = await LiveLocation.findOne({ guestId: guestId });

        const result = {
            rsvpDeleted: existingRsvp ? existingRsvp._id : null,
            locationDeleted: false
        };

        if (existingRsvp) {
            await InvitationRSVP.findOneAndDelete({ guest: guestId });
        }

        const guest = await Guest.findById(guestId).populate('party');
        
        const invitation = await Invitation.findById(guest.party.invitation);
        if (invitation) {
            invitation.savedGuests = invitation.savedGuests.filter(
                savedGuestId => savedGuestId.toString() !== guestId.toString()
            );
            invitation.sentToGuests = invitation.sentToGuests.filter(
                sentGuestId => sentGuestId.toString() !== guestId.toString()
            );
            await invitation.save();
        }

        if (existingLocation) {
            await LiveLocation.findOneAndDelete({ guestId: guestId });
            result.locationDeleted = true;
        }

        return result;
    } catch (error) {
        console.error('Error in deleteGuestRsvpAndLocation:', error);
        throw error;
    }
};

const guestResolvers = {
    PhoneNumber: PhoneNumberScalar,

    Guest: {
        user: async (parent) => {
            try {
                return await User.findById(parent.user);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting user');
            }
        },
        party: async (parent) => {
            try {
                return await Party.findById(parent.party);
            } catch (error) {
                console.error(error);
                throw new Error('Error getting party');
            }
        }
    },

    Query: {
        getGuestsByPartyId: async (_, { partyId, filter }) => {
            try {
                const query = await buildGuestQuery(filter || {});
                query.party = partyId;

                const guests = await Guest.find(query)
                    .populate('user')
                    .populate('party');
                
                if (guests.length === 0) {
                    return createResponse('PartyGuestsResponse', 'FAILURE', 'No guests found for this party', { 
                        result: { guests }
                    });
                }

                return createResponse('PartyGuestsResponse', 'SUCCESS', 'Guests fetched successfully', { 
                    result: { guests }
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error getting guests by party id', { 
                    errors: [{ field: 'partyId', message: error.message }]
                });
            }
        },
        getGuestById: async (_, { id }) => {
            try {
                const cachedGuest = await getByIdCache(id);
                if (cachedGuest) {
                    return createResponse('GuestResponse', 'SUCCESS', 'Guest retrieved successfully from cache', { 
                        result: { guest: cachedGuest } 
                    });
                }

                const guest = await Guest.findById(id);
                if (!guest) {
                    return createResponse('GuestErrorResponse', 'FAILURE', 'Guest not found', {
                        errors: [{ field: 'id', message: 'Guest not found' }]
                    });
                }

                await setCache(id, guest);
                return createResponse('GuestResponse', 'SUCCESS', 'Guest retrieved successfully', { 
                    result: { guest } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error retrieving guest', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        },

        getGuests: async (_, { filter, pagination }) => {
            try {
                const query = await buildGuestQuery(filter || {});
                const limit = pagination?.limit || 10;
                const skip = pagination?.skip || 0;

                const paginationInfo = await getPaginationInfo(Guest, query, limit, skip);
                
                const guests = await Guest.find(query)
                    .populate('user')
                    .populate('party')
                    .limit(limit)
                    .skip(skip)
                    .sort({ createdAt: -1 });

                if (guests.length === 0) {
                    return createResponse('GuestsResponse', 'FAILURE', 'No guests found', { 
                        result: { guests }, 
                        pagination: paginationInfo 
                    });
                }

                return createResponse('GuestsResponse', 'SUCCESS', 'Guests fetched successfully', { 
                    result: { guests }, 
                    pagination: paginationInfo 
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error getting guests', {
                    errors: [{ field: 'getGuests', message: error.message }]
                });
            }
        }
    },

    Mutation: {
        createGuestFromContact: async (_, { contact, partyId }) => {
            try {
                const user = await findOrCreateUser(contact);
                
                const existingGuest = await Guest.findOne({ user: user._id, party: partyId });
                if (existingGuest) {
                    return createResponse('GuestErrorResponse', 'FAILURE', 'Guest already exists for this party', {
                        errors: [{ field: 'createGuestFromContact', message: 'Guest already exists for this party' }]
                    });
                }

                const { guest } = await createGuestAndInvitation(user._id, partyId);
                return createResponse('GuestResponse', 'SUCCESS', 'Guest created successfully', { result: { guest } });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error creating guest', { 
                    errors: [{ field: 'createGuestFromContact', message: error.message }] 
                });
            }
        },
        createGuestsFromContacts: async (_, { contacts, partyId }) => {
            try {
                const party = await Party.findById(partyId);
                if (!party) {
                    return createResponse('GuestErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'partyId', message: 'Party not found' }]
                    });
                }

                const guests = await Promise.all(contacts.map(async (contact) => {
                    try {
                        const user = await findOrCreateUser(contact);
                        const existingGuest = await Guest.findOne({ user: user._id, party: partyId });
                        if (existingGuest) {
                            return existingGuest;
                        }
                        const { guest } = await createGuestAndInvitation(user._id, partyId);
                        return guest;
                    } catch (error) {
                        console.error(`Error processing contact ${contact.phone}:`, error);
                        return null;
                    }
                }));

                const validGuests = guests.filter(guest => guest !== null);
                const paginationInfo = {
                    totalItems: validGuests.length,
                    totalPages: 1,
                    currentPage: 1,
                    pageSize: validGuests.length
                };

                return createResponse('GuestsResponse', 'SUCCESS', 'Guests processed successfully', { 
                    result: { guests: validGuests },
                    pagination: paginationInfo
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error creating guests', { 
                    errors: [{ field: 'createGuestsFromContacts', message: error.message }] 
                });
            }
        },
        createGuest: async (_, { userId, partyId }) => {
            try {
                const validationError = await validateReferences({ userId, partyId }, guestIdSchema, 'Guest');
                if (validationError) {
                    return createResponse('GuestErrorResponse', 'FAILURE', validationError.message, {
                        errors: validationError.errors
                    });
                }

               const existingGuest = await Guest.findOne({ user: userId, party: partyId });
               if (existingGuest) {
                return createResponse('GuestErrorResponse', 'FAILURE', 'Guest already exists for this party', {
                    errors: [{ field: 'createGuest', message: 'Guest already exists for this party' }]
                });
               }

                const { guest, party } = await createGuestAndInvitation(userId, partyId);
                await setCache(party._id, party);

                return createResponse('GuestResponse', 'SUCCESS', 'Guest created successfully', { 
                    result: { guest } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error creating guest', {
                    errors: [{ field: 'userId', message: error.message }]
                });
            }
        },

        deleteGuest: async (_, { id }) => {
            try {
                const guest = await Guest.findById(id);
                if (!guest) {
                    return createResponse('GuestErrorResponse', 'FAILURE', 'Guest not found', {
                        errors: [{ field: 'id', message: 'Guest not found' }]
                    });
                }

                const party = await Party.findById(guest.party);
                if (!party) {
                    return createResponse('GuestErrorResponse', 'FAILURE', 'Party not found', {
                        errors: [{ field: 'id', message: 'Party not found' }]
                    });
                }

                // Delete RSVP and location data
                const result = await deleteGuestRsvpAndLocation(guest._id);

                // Remove guest from party
                party.guests = party.guests.filter(guestId => guestId?.toString() !== guest._id.toString());
                
                if (result.rsvpDeleted) {
                    party.rsvps = (party.rsvps || []).filter(rsvpId => 
                        rsvpId?.toString() !== result.rsvpDeleted.toString()
                    );
                }

                // Remove guest from any events
                await Event.updateMany(
                    { guests: guest._id },
                    { $pull: { guests: guest._id } }
                );

                // Remove any task collaborations
                await TaskCollaborator.deleteMany({ guest: guest._id });

                // Clean up any media associated with the guest
                const invitation = await Invitation.findOne({ party: guest.party });
                if (invitation) {
                    await deleteMediaAndRelatedData(invitation.media);
                }

                // Clean up any messages in party activity
                if (party.activity) {
                    party.activity = party.activity.filter(messageId => {
                        return true;
                    });
                }

                await party.save();
                await setCache(party._id, party);
                await Guest.findByIdAndDelete(guest._id);
                await clearCacheById(id);

                return createResponse('GuestResponse', 'SUCCESS', 'Guest deleted successfully', { 
                    result: { guest } 
                });
            } catch (error) {
                console.error(error);
                return createResponse('GuestErrorResponse', 'FAILURE', 'Error deleting guest', {
                    errors: [{ field: 'id', message: error.message }]
                });
            }
        }
    },

    
};

module.exports = guestResolvers; 